#!/usr/bin/env python3
"""
Script de test pour vérifier la fonctionnalité d'export Excel des réparations
"""

import sys
import os
import asyncio

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_excel_export_dependencies():
    """Test des dépendances nécessaires pour l'export Excel"""
    
    print("=== TEST DES DÉPENDANCES EXCEL ===\n")
    
    # Test pandas
    try:
        import pandas as pd
        print("✅ pandas importé avec succès")
        print(f"   Version: {pd.__version__}")
    except ImportError as e:
        print(f"❌ Erreur d'import pandas: {e}")
        return False
    
    # Test openpyxl
    try:
        import openpyxl
        print("✅ openpyxl importé avec succès")
        print(f"   Version: {openpyxl.__version__}")
    except ImportError as e:
        print(f"❌ Erreur d'import openpyxl: {e}")
        return False
    
    # Test des utilitaires d'export
    try:
        from app.utils.export_utils import export_to_excel
        print("✅ export_to_excel importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur d'import export_to_excel: {e}")
        return False
    
    return True

def test_repair_data_preparation():
    """Test de la préparation des données de réparation"""
    
    print("\n=== TEST DE PRÉPARATION DES DONNÉES ===\n")
    
    try:
        from app.utils.database import SessionLocal
        from app.core.services.repair_service import RepairService
        
        # Créer une session de base de données
        db = SessionLocal()
        repair_service = RepairService(db)
        
        print("✅ Services de réparation initialisés")
        
        # Récupérer quelques réparations pour test
        repairs = asyncio.run(repair_service.get_all(limit=5))
        print(f"📊 {len(repairs)} réparations récupérées pour test")
        
        if repairs:
            print("   Exemple de réparation:")
            repair = repairs[0]
            print(f"   - Numéro: {repair.number}")
            print(f"   - Client: {repair.customer_name}")
            print(f"   - Statut: {repair.status}")
            print(f"   - Marque: {repair.brand}")
            print(f"   - Modèle: {repair.model}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des données: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_export_functionality():
    """Test de la fonctionnalité d'export Excel"""
    
    print("\n=== TEST DE LA FONCTIONNALITÉ D'EXPORT ===\n")
    
    try:
        import pandas as pd
        from app.utils.export_utils import export_to_excel
        import tempfile
        import os
        
        # Créer des données de test
        test_data = {
            "Réparations": pd.DataFrame({
                "Numéro": ["REP-001", "REP-002", "REP-003"],
                "Client": ["Client A", "Client B", "Client C"],
                "Statut": ["En cours", "Terminé", "En attente"],
                "Marque": ["Samsung", "Apple", "Huawei"],
                "Modèle": ["Galaxy S21", "iPhone 12", "P30 Pro"]
            }),
            "Résumé": pd.DataFrame({
                "Métrique": ["Total réparations", "En cours", "Terminées"],
                "Valeur": [3, 1, 1]
            }),
            "Par Statut": pd.DataFrame({
                "Statut": ["En cours", "Terminé", "En attente"],
                "Nombre": [1, 1, 1],
                "Pourcentage": ["33.3%", "33.3%", "33.3%"]
            })
        }
        
        # Créer un fichier temporaire
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        # Tester l'export
        success = export_to_excel(test_data, temp_path)
        
        if success:
            print("✅ Export Excel réussi")
            print(f"   Fichier créé: {temp_path}")
            
            # Vérifier que le fichier existe et a une taille > 0
            if os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                print(f"   Taille du fichier: {os.path.getsize(temp_path)} bytes")
                
                # Tester la lecture du fichier
                try:
                    with pd.ExcelFile(temp_path) as excel_file:
                        sheet_names = excel_file.sheet_names
                        print(f"   Onglets créés: {sheet_names}")
                        
                        # Lire un onglet pour vérifier
                        df = pd.read_excel(temp_path, sheet_name="Réparations")
                        print(f"   Lignes dans l'onglet Réparations: {len(df)}")
                        
                except Exception as e:
                    print(f"⚠️  Erreur lors de la lecture du fichier: {e}")
            else:
                print("❌ Fichier non créé ou vide")
                success = False
        else:
            print("❌ Échec de l'export Excel")
        
        # Nettoyer le fichier temporaire
        try:
            os.unlink(temp_path)
            print("🧹 Fichier temporaire nettoyé")
        except:
            pass
        
        return success
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'export: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    
    print("🧪 TEST DE LA FONCTIONNALITÉ D'EXPORT EXCEL DES RÉPARATIONS\n")
    
    # Test des dépendances
    deps_ok = test_excel_export_dependencies()
    
    if not deps_ok:
        print("\n❌ Les dépendances ne sont pas satisfaites. Arrêt des tests.")
        return False
    
    # Test de préparation des données
    data_ok = test_repair_data_preparation()
    
    # Test de la fonctionnalité d'export
    export_ok = test_excel_export_functionality()
    
    # Résumé
    print("\n" + "="*50)
    print("RÉSUMÉ DES TESTS:")
    print(f"✅ Dépendances: {'OK' if deps_ok else 'ÉCHEC'}")
    print(f"✅ Données: {'OK' if data_ok else 'ÉCHEC'}")
    print(f"✅ Export Excel: {'OK' if export_ok else 'ÉCHEC'}")
    
    all_ok = deps_ok and data_ok and export_ok
    
    if all_ok:
        print("\n🎉 TOUS LES TESTS SONT PASSÉS!")
        print("💡 Vous pouvez maintenant utiliser l'export Excel dans l'interface des réparations.")
    else:
        print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus avant d'utiliser la fonctionnalité.")
    
    return all_ok

if __name__ == "__main__":
    main()

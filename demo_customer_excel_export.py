#!/usr/bin/env python3
"""
Démonstration de la fonctionnalité d'export Excel des clients
"""

import sys
import os
import tempfile

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import Qt

def create_demo_customer_excel_export():
    """Crée un fichier Excel de démonstration avec les données des clients"""
    
    print("=== DÉMONSTRATION D'EXPORT EXCEL DES CLIENTS ===\n")
    
    try:
        import pandas as pd
        from app.utils.export_utils import export_to_excel
        from datetime import datetime
        
        # Créer des données de démonstration réalistes
        clients_data = pd.DataFrame({
            "ID": [1, 2, 3, 4, 5, 6, 7, 8],
            "Nom": [
                "<PERSON>",
                "<PERSON><PERSON>", 
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON><PERSON>ouri",
                "<PERSON>cef <PERSON>rah<PERSON>"
            ],
            "Contact": [
                "<PERSON> <PERSON><PERSON>",
                "<PERSON><PERSON> <PERSON>helifi",
                "Karim <PERSON>ziane", 
                "Smail <PERSON>ai",
                "<PERSON> <PERSON>udiaf",
                "<PERSON> <PERSON>j",
                "<PERSON>ila <PERSON>ouri",
                "<PERSON>cef Brahim"
            ],
            "Té<PERSON>phone": [
                "0555123456",
                "0661789012",
                "0770345678", 
                "0698765432",
                "0777654321",
                "0556987654",
                "0662345678",
                "0771234567"
            ],
            "Email": [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            ],
            "Adresse": [
                "Rue des Martyrs, Cité El Badr",
                "Avenue de l'Indépendance",
                "Boulevard Mohamed V",
                "Rue Didouche Mourad",
                "Cité 1000 Logements",
                "Rue Larbi Ben M'hidi",
                "Avenue Souidania",
                "Rue Hassiba Ben Bouali"
            ],
            "Commune": [
                "Bab Ezzouar",
                "Es Senia",
                "El Khroub",
                "Sidi M'Hamed",
                "El Bouni",
                "Ouled Fayet",
                "Cheraga",
                "Rouiba"
            ],
            "Ville": [
                "Alger",
                "Oran",
                "Constantine",
                "Alger",
                "Annaba",
                "Alger",
                "Alger",
                "Alger"
            ],
            "Limite crédit": [
                "5000.00",
                "3000.00",
                "4000.00",
                "2000.00",
                "3500.00",
                "6000.00",
                "2500.00",
                "4500.00"
            ],
            "Solde actuel": [
                "0.00",
                "150.00",
                "-200.00",
                "500.00",
                "0.00",
                "750.00",
                "-100.00",
                "300.00"
            ]
        })
        
        # Créer les données de résumé
        summary_data = pd.DataFrame({
            "Métrique": [
                "Nombre total de clients",
                "Date d'export",
                "Villes représentées",
                "Clients à Alger",
                "Clients à Oran",
                "Clients à Constantine",
                "Clients à Annaba",
                "Limite de crédit totale",
                "Solde total",
                "Clients endettés",
                "Clients à jour"
            ],
            "Valeur": [
                8,
                datetime.now().strftime("%d/%m/%Y %H:%M"),
                4,
                5,
                1,
                1,
                1,
                "30500.00 DA",
                "1400.00 DA",
                4,
                4
            ]
        })
        
        # Créer les données par ville
        city_data = pd.DataFrame({
            "Ville": ["Alger", "Oran", "Constantine", "Annaba"],
            "Nombre de clients": [5, 1, 1, 1],
            "Pourcentage": ["62.5%", "12.5%", "12.5%", "12.5%"]
        })
        
        # Créer les données par statut
        status_data = pd.DataFrame({
            "Statut": ["À jour", "En retard", "Créditeurs"],
            "Nombre de clients": [2, 4, 2],
            "Pourcentage": ["25.0%", "50.0%", "25.0%"]
        })
        
        # Créer l'analyse financière
        financial_data = pd.DataFrame({
            "Métrique": [
                "Limite de crédit totale",
                "Limite de crédit moyenne",
                "Limite de crédit maximale",
                "Solde total",
                "Solde moyen",
                "Dette maximale",
                "Crédit maximal accordé",
                "Clients avec crédit",
                "Clients endettés",
                "Taux d'endettement",
                "Taux de clients avec crédit"
            ],
            "Valeur": [
                "30500.00 DA",
                "3812.50 DA",
                "6000.00 DA",
                "1400.00 DA",
                "175.00 DA",
                "750.00 DA",
                "-200.00 DA",
                8,
                4,
                "50.0%",
                "100.0%"
            ]
        })
        
        # Préparer toutes les données pour l'export
        export_data = {
            "Clients": clients_data,
            "Résumé": summary_data,
            "Par Ville": city_data,
            "Par Statut": status_data,
            "Analyse Financière": financial_data
        }
        
        # Créer le fichier Excel
        output_path = os.path.expanduser("~/Documents/Demo_Clients_Export.xlsx")
        
        if export_to_excel(export_data, output_path):
            print("✅ Fichier Excel de démonstration créé avec succès!")
            print(f"📁 Emplacement: {output_path}")
            print(f"📊 Onglets créés: {list(export_data.keys())}")
            
            # Vérifier la taille du fichier
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📏 Taille du fichier: {file_size} bytes")
                
                # Afficher un aperçu des données
                print("\n📋 APERÇU DES DONNÉES:")
                print("   Clients:")
                for _, row in clients_data.head(3).iterrows():
                    print(f"   - {row['Nom']}: {row['Téléphone']} ({row['Ville']}) - Solde: {row['Solde actuel']} DA")
                
                print(f"\n   Statistiques:")
                print(f"   - Total: {len(clients_data)} clients")
                print(f"   - Villes: {len(city_data)} villes représentées")
                print(f"   - Clients endettés: 4 clients")
                print(f"   - Limite de crédit totale: 30500.00 DA")
                
                return output_path
            else:
                print("❌ Le fichier n'a pas été créé")
                return None
        else:
            print("❌ Échec de la création du fichier Excel")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lors de la création du fichier de démonstration: {e}")
        import traceback
        traceback.print_exc()
        return None

def show_demo_instructions():
    """Affiche les instructions pour utiliser la fonctionnalité d'export"""
    
    print("\n" + "="*60)
    print("📖 GUIDE D'UTILISATION DE L'EXPORT EXCEL DES CLIENTS")
    print("="*60)
    
    print("\n🚀 COMMENT UTILISER LA FONCTIONNALITÉ:")
    print("   1. Lancez l'application principale (main.py)")
    print("   2. Connectez-vous avec vos identifiants")
    print("   3. Naviguez vers le module 'Clients'")
    print("   4. Cliquez sur le bouton 'Exporter' dans la barre d'outils")
    print("   5. Choisissez le format d'export:")
    print("      • Excel (.xlsx) - Recommandé pour l'analyse")
    print("      • CSV (.csv) - Compatible avec tous les tableurs")
    print("   6. Sélectionnez l'emplacement de sauvegarde")
    print("   7. Le fichier sera créé automatiquement")
    
    print("\n📊 CONTENU DU FICHIER EXCEL:")
    print("   • Onglet 'Clients': Toutes les données des clients")
    print("   • Onglet 'Résumé': Statistiques générales et date d'export")
    print("   • Onglet 'Par Ville': Répartition des clients par ville")
    print("   • Onglet 'Par Statut': Répartition par statut financier")
    print("   • Onglet 'Analyse Financière': Analyse des crédits et soldes")
    
    print("\n💡 AVANTAGES DE L'EXPORT EXCEL:")
    print("   ✅ Analyse avancée avec formules et graphiques")
    print("   ✅ Filtrage et tri des données clients")
    print("   ✅ Partage facile avec d'autres services")
    print("   ✅ Archivage et sauvegarde des données")
    print("   ✅ Rapports financiers personnalisés")
    print("   ✅ Suivi des créances et des paiements")
    
    print("\n🔧 DÉPANNAGE:")
    print("   • Si l'export échoue, vérifiez l'espace disque disponible")
    print("   • Assurez-vous que le fichier de destination n'est pas ouvert")
    print("   • Vérifiez les permissions d'écriture du dossier")
    print("   • En cas d'erreur, essayez l'export CSV en alternative")

def main():
    """Fonction principale de démonstration"""
    
    print("🎯 DÉMONSTRATION DE L'EXPORT EXCEL DES CLIENTS\n")
    
    # Créer le fichier de démonstration
    demo_file = create_demo_customer_excel_export()
    
    # Afficher les instructions
    show_demo_instructions()
    
    if demo_file:
        print(f"\n🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS!")
        print(f"📁 Fichier de démonstration créé: {demo_file}")
        print("\n💻 Vous pouvez maintenant:")
        print("   1. Ouvrir le fichier Excel pour voir le résultat")
        print("   2. Tester la fonctionnalité dans l'application")
        print("   3. Exporter vos vraies données de clients")
    else:
        print("\n⚠️  La démonstration a échoué")
        print("🔧 Vérifiez les erreurs ci-dessus et réessayez")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Script de test pour vérifier la fonctionnalité d'export Excel des clients
"""

import sys
import os
import asyncio

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_customer_excel_export_dependencies():
    """Test des dépendances nécessaires pour l'export Excel des clients"""
    
    print("=== TEST DES DÉPENDANCES EXCEL CLIENTS ===\n")
    
    # Test pandas
    try:
        import pandas as pd
        print("✅ pandas importé avec succès")
        print(f"   Version: {pd.__version__}")
    except ImportError as e:
        print(f"❌ Erreur d'import pandas: {e}")
        return False
    
    # Test openpyxl
    try:
        import openpyxl
        print("✅ openpyxl importé avec succès")
        print(f"   Version: {openpyxl.__version__}")
    except ImportError as e:
        print(f"❌ Erreur d'import openpyxl: {e}")
        return False
    
    # Test des utilitaires d'export
    try:
        from app.utils.export_utils import export_to_excel
        print("✅ export_to_excel importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur d'import export_to_excel: {e}")
        return False
    
    return True

def test_customer_data_preparation():
    """Test de la préparation des données de clients"""
    
    print("\n=== TEST DE PRÉPARATION DES DONNÉES CLIENTS ===\n")
    
    try:
        from app.utils.database import SessionLocal
        from app.core.services.customer_service import CustomerService
        
        # Créer une session de base de données
        db = SessionLocal()
        customer_service = CustomerService(db)
        
        print("✅ Services de clients initialisés")
        
        # Récupérer quelques clients pour test
        customers = asyncio.run(customer_service.get_all(limit=5))
        print(f"📊 {len(customers)} clients récupérés pour test")
        
        if customers:
            print("   Exemple de client:")
            customer = customers[0]
            print(f"   - Nom: {customer.name}")
            print(f"   - Téléphone: {customer.phone}")
            print(f"   - Email: {customer.email}")
            print(f"   - Ville: {customer.city}")
            print(f"   - Limite crédit: {customer.credit_limit}")
            print(f"   - Solde actuel: {customer.current_balance}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des données: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_ui_export():
    """Test de l'interface utilisateur d'export des clients"""
    
    print("\n=== TEST DE L'INTERFACE D'EXPORT CLIENTS ===\n")
    
    try:
        from PyQt6.QtWidgets import QApplication
        
        # Créer l'application Qt
        app = QApplication(sys.argv)
        
        from app.ui.views.customer.customer_view import CustomerView
        
        print("✅ CustomerView importée avec succès")
        
        # Créer la vue
        customer_view = CustomerView()
        print("✅ CustomerView créée avec succès")
        
        # Vérifier que le bouton d'export existe
        if hasattr(customer_view, 'export_button'):
            print("✅ Bouton d'export trouvé")
        else:
            print("❌ Bouton d'export non trouvé")
        
        # Vérifier que les méthodes d'export existent
        methods_to_check = [
            'export_customers',
            '_export_customers_to_csv', 
            '_export_customers_to_excel',
            '_prepare_customer_data_for_excel',
            '_prepare_customer_summary_stats',
            '_prepare_customer_city_breakdown',
            '_prepare_customer_status_breakdown',
            '_prepare_customer_financial_analysis'
        ]
        
        print("\n📋 Vérification des méthodes d'export:")
        for method_name in methods_to_check:
            if hasattr(customer_view, method_name):
                print(f"   ✅ {method_name}")
            else:
                print(f"   ❌ {method_name}")
        
        # Vérifier que le tableau existe
        if hasattr(customer_view, 'table_view'):
            print("\n✅ TableView trouvée")
            
            # Vérifier le modèle
            if hasattr(customer_view, 'table_model'):
                print("✅ TableModel trouvé")
            else:
                print("❌ TableModel non trouvé")
        else:
            print("\n❌ TableView non trouvée")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de l'interface: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_export_functionality():
    """Test de la fonctionnalité d'export Excel des clients"""
    
    print("\n=== TEST DE LA FONCTIONNALITÉ D'EXPORT CLIENTS ===\n")
    
    try:
        import pandas as pd
        from app.utils.export_utils import export_to_excel
        import tempfile
        import os
        
        # Créer des données de test
        test_data = {
            "Clients": pd.DataFrame({
                "ID": [1, 2, 3, 4, 5],
                "Nom": ["Ahmed Benali", "Fatima Khelifi", "Karim Meziane", "Smail Benai", "Nadia Boudiaf"],
                "Téléphone": ["0555123456", "0661789012", "0770345678", "0698765432", "0777654321"],
                "Email": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
                "Ville": ["Alger", "Oran", "Constantine", "Alger", "Annaba"],
                "Limite crédit": ["5000.00", "3000.00", "4000.00", "2000.00", "3500.00"],
                "Solde actuel": ["0.00", "150.00", "-200.00", "500.00", "0.00"]
            }),
            "Résumé": pd.DataFrame({
                "Métrique": ["Total clients", "Villes représentées", "Clients endettés"],
                "Valeur": [5, 4, 2]
            }),
            "Par Ville": pd.DataFrame({
                "Ville": ["Alger", "Oran", "Constantine", "Annaba"],
                "Nombre de clients": [2, 1, 1, 1],
                "Pourcentage": ["40.0%", "20.0%", "20.0%", "20.0%"]
            }),
            "Analyse Financière": pd.DataFrame({
                "Métrique": ["Limite de crédit totale", "Solde total", "Taux d'endettement"],
                "Valeur": ["17500.00 DA", "450.00 DA", "40.0%"]
            })
        }
        
        # Créer un fichier temporaire
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        # Tester l'export
        success = export_to_excel(test_data, temp_path)
        
        if success:
            print("✅ Export Excel réussi")
            print(f"   Fichier créé: {temp_path}")
            
            # Vérifier que le fichier existe et a une taille > 0
            if os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                print(f"   Taille du fichier: {os.path.getsize(temp_path)} bytes")
                
                # Tester la lecture du fichier
                try:
                    with pd.ExcelFile(temp_path) as excel_file:
                        sheet_names = excel_file.sheet_names
                        print(f"   Onglets créés: {sheet_names}")
                        
                        # Lire un onglet pour vérifier
                        df = pd.read_excel(temp_path, sheet_name="Clients")
                        print(f"   Lignes dans l'onglet Clients: {len(df)}")
                        
                except Exception as e:
                    print(f"⚠️  Erreur lors de la lecture du fichier: {e}")
            else:
                print("❌ Fichier non créé ou vide")
                success = False
        else:
            print("❌ Échec de l'export Excel")
        
        # Nettoyer le fichier temporaire
        try:
            os.unlink(temp_path)
            print("🧹 Fichier temporaire nettoyé")
        except:
            pass
        
        return success
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'export: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    
    print("🧪 TEST DE LA FONCTIONNALITÉ D'EXPORT EXCEL DES CLIENTS\n")
    
    # Test des dépendances
    deps_ok = test_customer_excel_export_dependencies()
    
    if not deps_ok:
        print("\n❌ Les dépendances ne sont pas satisfaites. Arrêt des tests.")
        return False
    
    # Test de préparation des données
    data_ok = test_customer_data_preparation()
    
    # Test de l'interface utilisateur
    ui_ok = test_customer_ui_export()
    
    # Test de la fonctionnalité d'export
    export_ok = test_customer_export_functionality()
    
    # Résumé
    print("\n" + "="*50)
    print("RÉSUMÉ DES TESTS:")
    print(f"✅ Dépendances: {'OK' if deps_ok else 'ÉCHEC'}")
    print(f"✅ Données: {'OK' if data_ok else 'ÉCHEC'}")
    print(f"✅ Interface: {'OK' if ui_ok else 'ÉCHEC'}")
    print(f"✅ Export Excel: {'OK' if export_ok else 'ÉCHEC'}")
    
    all_ok = deps_ok and data_ok and ui_ok and export_ok
    
    if all_ok:
        print("\n🎉 TOUS LES TESTS SONT PASSÉS!")
        print("💡 Vous pouvez maintenant utiliser l'export Excel dans l'interface des clients.")
    else:
        print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus avant d'utiliser la fonctionnalité.")
    
    return all_ok

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Script de test pour vérifier l'affichage des fournisseurs dans l'interface de sélection de produits
"""

import sys
import os
import asyncio

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from app.ui.views.sale.dialogs.product_selection_dialog import ProductSelectionDialog

def test_product_selection_dialog():
    """Test de la boîte de dialogue de sélection de produits"""
    
    print("=== TEST DE L'INTERFACE DE SÉLECTION DE PRODUITS ===\n")
    
    # Créer l'application Qt
    app = QApplication(sys.argv)
    
    try:
        # Créer la boîte de dialogue
        dialog = ProductSelectionDialog()
        
        print("✅ Boîte de dialogue créée avec succès")
        print(f"📊 Nombre de produits chargés: {len(dialog.filtered_products)}")
        print(f"🏢 Nombre de fournisseurs chargés: {len(dialog.suppliers)}")
        
        # Vérifier l'affichage des fournisseurs dans le tableau
        print("\n📋 Vérification de l'affichage des fournisseurs:")
        
        for i in range(min(10, dialog.products_table.rowCount())):
            sku_item = dialog.products_table.item(i, 0)
            name_item = dialog.products_table.item(i, 1)
            supplier_item = dialog.products_table.item(i, 5)
            
            if sku_item and name_item and supplier_item:
                print(f"   {i+1}. {name_item.text()[:30]}... -> {supplier_item.text()}")
        
        # Vérifier le combo des fournisseurs
        print(f"\n🔽 Fournisseurs dans le combo de filtrage:")
        for i in range(dialog.supplier_combo.count()):
            text = dialog.supplier_combo.itemText(i)
            data = dialog.supplier_combo.itemData(i)
            print(f"   {i}. {text} (ID: {data})")
        
        print("\n✅ Test terminé avec succès!")
        print("💡 Vous pouvez maintenant tester manuellement l'interface en lançant l'application.")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

if __name__ == "__main__":
    test_product_selection_dialog()

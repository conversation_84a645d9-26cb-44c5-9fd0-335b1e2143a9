#!/usr/bin/env python3
"""
Démonstration de la fonctionnalité d'export Excel des fournisseurs
"""

import sys
import os
import tempfile

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import Qt

def create_demo_supplier_excel_export():
    """Crée un fichier Excel de démonstration avec les données des fournisseurs"""
    
    print("=== DÉMONSTRATION D'EXPORT EXCEL DES FOURNISSEURS ===\n")
    
    try:
        import pandas as pd
        from app.utils.export_utils import export_to_excel
        from datetime import datetime
        
        # Créer des données de démonstration réalistes
        suppliers_data = pd.DataFrame({
            "ID": [1, 2, 3, 4, 5, 6, 7, 8],
            "Nom": [
                "TechnoPlus SARL",
                "ElectroMag Distribution", 
                "Informatique Solutions",
                "Digital World",
                "Micro Systems",
                "Electronic Parts",
                "Computer Zone",
                "Tech Supply"
            ],
            "Contact": [
                "Ahmed Benali",
                "Fatima Khelifi",
                "Karim Meziane", 
                "Smail Benai",
                "Nadia Boudiaf",
                "Omar Hadj",
                "Leila Mansouri",
                "Youcef Brahim"
            ],
            "Téléphone": [
                "0555123456",
                "0661789012",
                "0770345678", 
                "0698765432",
                "0777654321",
                "0556987654",
                "0662345678",
                "0771234567"
            ],
            "Email": [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            ],
            "Adresse": [
                "Zone Industrielle Rouiba",
                "Rue de la République",
                "Boulevard Mohamed V",
                "Avenue de l'Indépendance",
                "Cité 1000 Logements",
                "Rue Larbi Ben M'hidi",
                "Avenue Souidania",
                "Rue Hassiba Ben Bouali"
            ],
            "Commune": [
                "Rouiba",
                "Es Senia",
                "El Khroub",
                "Sidi M'Hamed",
                "El Bouni",
                "Ouled Fayet",
                "Cheraga",
                "Bab Ezzouar"
            ],
            "Ville": [
                "Alger",
                "Oran",
                "Constantine",
                "Alger",
                "Annaba",
                "Alger",
                "Alger",
                "Alger"
            ],
            "Note": [
                "Excellent",
                "Bon",
                "Moyen",
                "Bon",
                "Excellent",
                "Médiocre",
                "Bon",
                "Excellent"
            ]
        })
        
        # Créer les données de résumé
        summary_data = pd.DataFrame({
            "Métrique": [
                "Nombre total de fournisseurs",
                "Date d'export",
                "Villes représentées",
                "Fournisseurs à Alger",
                "Fournisseurs à Oran",
                "Fournisseurs à Constantine",
                "Fournisseurs à Annaba",
                "Fournisseurs excellents",
                "Fournisseurs bons",
                "Fournisseurs moyens",
                "Fournisseurs médiocres"
            ],
            "Valeur": [
                8,
                datetime.now().strftime("%d/%m/%Y %H:%M"),
                4,
                5,
                1,
                1,
                1,
                3,
                3,
                1,
                1
            ]
        })
        
        # Créer les données par ville
        city_data = pd.DataFrame({
            "Ville": ["Alger", "Oran", "Constantine", "Annaba"],
            "Nombre de fournisseurs": [5, 1, 1, 1],
            "Pourcentage": ["62.5%", "12.5%", "12.5%", "12.5%"]
        })
        
        # Créer les données par note
        rating_data = pd.DataFrame({
            "Note": ["Excellent", "Bon", "Moyen", "Médiocre"],
            "Nombre de fournisseurs": [3, 3, 1, 1],
            "Pourcentage": ["37.5%", "37.5%", "12.5%", "12.5%"]
        })
        
        # Créer l'analyse des évaluations
        evaluation_data = pd.DataFrame({
            "Métrique": [
                "Fournisseurs excellents",
                "Fournisseurs bons",
                "Fournisseurs moyens",
                "Fournisseurs médiocres",
                "Fournisseurs mauvais",
                "Fournisseurs non évalués",
                "Fournisseurs de qualité (Excellent + Bon)",
                "Pourcentage de qualité",
                "Fournisseurs à améliorer (Médiocre + Mauvais)",
                "Pourcentage à améliorer",
                "Taux d'évaluation"
            ],
            "Valeur": [
                3,
                3,
                1,
                1,
                0,
                0,
                6,
                "75.0%",
                1,
                "12.5%",
                "100.0%"
            ]
        })
        
        # Préparer toutes les données pour l'export
        export_data = {
            "Fournisseurs": suppliers_data,
            "Résumé": summary_data,
            "Par Ville": city_data,
            "Par Note": rating_data,
            "Évaluations": evaluation_data
        }
        
        # Créer le fichier Excel
        output_path = os.path.expanduser("~/Documents/Demo_Fournisseurs_Export.xlsx")
        
        if export_to_excel(export_data, output_path):
            print("✅ Fichier Excel de démonstration créé avec succès!")
            print(f"📁 Emplacement: {output_path}")
            print(f"📊 Onglets créés: {list(export_data.keys())}")
            
            # Vérifier la taille du fichier
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📏 Taille du fichier: {file_size} bytes")
                
                # Afficher un aperçu des données
                print("\n📋 APERÇU DES DONNÉES:")
                print("   Fournisseurs:")
                for _, row in suppliers_data.head(3).iterrows():
                    print(f"   - {row['Nom']}: {row['Contact']} ({row['Ville']}) - Note: {row['Note']}")
                
                print(f"\n   Statistiques:")
                print(f"   - Total: {len(suppliers_data)} fournisseurs")
                print(f"   - Villes: {len(city_data)} villes représentées")
                print(f"   - Fournisseurs excellents: 3 fournisseurs")
                print(f"   - Fournisseurs de qualité: 6 fournisseurs (75%)")
                
                return output_path
            else:
                print("❌ Le fichier n'a pas été créé")
                return None
        else:
            print("❌ Échec de la création du fichier Excel")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lors de la création du fichier de démonstration: {e}")
        import traceback
        traceback.print_exc()
        return None

def show_demo_instructions():
    """Affiche les instructions pour utiliser la fonctionnalité d'export"""
    
    print("\n" + "="*60)
    print("📖 GUIDE D'UTILISATION DE L'EXPORT EXCEL DES FOURNISSEURS")
    print("="*60)
    
    print("\n🚀 COMMENT UTILISER LA FONCTIONNALITÉ:")
    print("   1. Lancez l'application principale (main.py)")
    print("   2. Connectez-vous avec vos identifiants")
    print("   3. Naviguez vers le module 'Fournisseurs'")
    print("   4. Cliquez sur le bouton 'Exporter' dans la barre d'outils")
    print("   5. Choisissez le format d'export:")
    print("      • Excel (.xlsx) - Recommandé pour l'analyse")
    print("      • CSV (.csv) - Compatible avec tous les tableurs")
    print("   6. Sélectionnez l'emplacement de sauvegarde")
    print("   7. Le fichier sera créé automatiquement")
    
    print("\n📊 CONTENU DU FICHIER EXCEL:")
    print("   • Onglet 'Fournisseurs': Toutes les données des fournisseurs")
    print("   • Onglet 'Résumé': Statistiques générales et date d'export")
    print("   • Onglet 'Par Ville': Répartition des fournisseurs par ville")
    print("   • Onglet 'Par Note': Répartition par note d'évaluation")
    print("   • Onglet 'Évaluations': Analyse détaillée des performances")
    
    print("\n💡 AVANTAGES DE L'EXPORT EXCEL:")
    print("   ✅ Analyse avancée avec formules et graphiques")
    print("   ✅ Filtrage et tri des données fournisseurs")
    print("   ✅ Partage facile avec d'autres services")
    print("   ✅ Archivage et sauvegarde des données")
    print("   ✅ Rapports de performance personnalisés")
    print("   ✅ Suivi des évaluations et de la qualité")
    
    print("\n🔧 DÉPANNAGE:")
    print("   • Si l'export échoue, vérifiez l'espace disque disponible")
    print("   • Assurez-vous que le fichier de destination n'est pas ouvert")
    print("   • Vérifiez les permissions d'écriture du dossier")
    print("   • En cas d'erreur, essayez l'export CSV en alternative")

def main():
    """Fonction principale de démonstration"""
    
    print("🎯 DÉMONSTRATION DE L'EXPORT EXCEL DES FOURNISSEURS\n")
    
    # Créer le fichier de démonstration
    demo_file = create_demo_supplier_excel_export()
    
    # Afficher les instructions
    show_demo_instructions()
    
    if demo_file:
        print(f"\n🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS!")
        print(f"📁 Fichier de démonstration créé: {demo_file}")
        print("\n💻 Vous pouvez maintenant:")
        print("   1. Ouvrir le fichier Excel pour voir le résultat")
        print("   2. Tester la fonctionnalité dans l'application")
        print("   3. Exporter vos vraies données de fournisseurs")
    else:
        print("\n⚠️  La démonstration a échoué")
        print("🔧 Vérifiez les erreurs ci-dessus et réessayez")

if __name__ == "__main__":
    main()

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QFrame, QMessageBox, QMenu, QTabWidget, QHeaderView
)
from PyQt6.QtCore import Qt, QSortFilterProxyModel, QTimer, QPoint
from PyQt6.QtGui import QIcon, QAction, QCursor
import asyncio

from .customer_table_model import CustomerTableModel
from .dialogs.customer_dialog import CustomerDialog
from ...components.custom_widgets import SearchBar, LoadingOverlay, FilterComboBox
from app.core.services.customer_service import CustomerService
from app.utils.export_utils import export_table_data, export_to_excel

class CustomerView(QWidget):
    """Vue principale pour la gestion des clients"""

    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        # Utiliser QTimer pour planifier correctement le chargement asynchrone
        QTimer.singleShot(0, self._init_data)
        # Permissions: à appliquer après authentification
        self._permissions_applied = False

    def apply_permissions(self, auth_controller):
        """Active/désactive les boutons selon les permissions utilisateur."""
        if not hasattr(auth_controller, 'has_permission'):
            return
        self.add_button.setEnabled(auth_controller.has_permission('customer.create'))
        self.edit_button.setEnabled(auth_controller.has_permission('customer.edit'))
        self.delete_button.setEnabled(auth_controller.has_permission('customer.delete'))
        self._permissions_applied = True

    def _init_data(self):
        """Initialise le chargement des données"""
        print("Initialisation des données clients...")
        # Utiliser notre wrapper pour exécuter load_data de manière asynchrone
        self._load_data_wrapper()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)



        # Barre d'outils (style inspiré de Réparation)
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(18)

        button_style = (
            "QPushButton { background: #f5f7fa; color: #1976D2; border: 1px solid #e0e0e0; border-radius: 6px; padding: 8px 18px; font-weight: 600; } "
            "QPushButton:hover { background: #e3f0fc; color: #1565c0; border: 1px solid #90caf9; } "
            "QPushButton:pressed { background: #bbdefb; color: #0d47a1; } "
            "QPushButton:disabled { background: #f5f5f5; color: #bdbdbd; border: 1px solid #eeeeee; } "
        )

        # Bouton d'ajout
        self.add_button = QPushButton("Nouveau Client")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.add_button)

        # Bouton de modification
        self.edit_button = QPushButton("Modifier")
        self.edit_button.setIcon(QIcon("app/ui/resources/icons/edit.svg"))
        self.edit_button.setEnabled(False)
        self.edit_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.edit_button)

        # Bouton de suppression
        self.delete_button = QPushButton("Supprimer")
        self.delete_button.setIcon(QIcon("app/ui/resources/icons/delete.svg"))
        self.delete_button.setEnabled(False)
        self.delete_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.delete_button)

        # Bouton d'export
        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        self.export_button.setStyleSheet(button_style)
        toolbar_layout.addWidget(self.export_button)

        # Filtres (recherche + statut client)
        toolbar_layout.addStretch(1)

        # Barre de recherche (style inspiré)
        self.search_bar = SearchBar("Rechercher un client...")
        self.search_bar.setFixedHeight(36)
        self.search_bar.setStyleSheet(
            """
            QLineEdit {
                background: #f5f7fa;
                border: 1.5px solid #e0e0e0;
                border-radius: 18px;
                padding-left: 36px;
                font-size: 15px;
                color: #222;
            }
            QLineEdit:focus {
                border: 1.5px solid #1976D2;
                background: #fff;
            }
            """
        )
        toolbar_layout.addWidget(self.search_bar, stretch=2)

        # Filtre de statut client
        combo_style = (
            "QComboBox { background: #f5f7fa; border: 1.5px solid #e0e0e0; border-radius: 16px; padding: 6px 24px 6px 14px; font-size: 15px; color: #1976D2; min-width: 160px; } "
            "QComboBox:focus { border: 1.5px solid #1976D2; background: #fff; } "
            "QComboBox::drop-down { border: none; } "
            "QComboBox QAbstractItemView { background: #fff; border-radius: 8px; } "
        )
        self.status_filter = FilterComboBox("statut clients")
        self.status_filter.setStyleSheet(combo_style)
        self.status_filter.setFixedHeight(36)
        self.status_filter.addItem("Clients à jour", userData="up_to_date")
        self.status_filter.addItem("Clients non à jour", userData="not_up_to_date")
        self.status_filter.currentIndexChanged.connect(self.apply_status_filter)
        toolbar_layout.addWidget(self.status_filter, stretch=1)

        main_layout.addLayout(toolbar_layout)

        # Panneau de détails (onglets) sous la barre d'outils
        self.details_tabs = QTabWidget()
        self.details_tabs.setObjectName("customerDetailsTabs")
        # Fond et style comme Réparation (éviter fond blanc)
        try:
            self.details_tabs.setStyleSheet("QTabWidget::pane { background: #FFFFFF; } QStackedWidget, QStackedWidget > QWidget { background: #FFFFFF; color: #212121; }")
        except Exception:
            pass
        # Onglets basiques (placeholders pour l’instant)
        from app.ui.views.customer.widgets.customer_balance_widget import CustomerBalanceWidget
        from app.ui.views.customer.widgets.customer_info_widget import CustomerInfoWidget
        from app.ui.views.customer.widgets.customer_payments_widget import CustomerPaymentsWidget
        from app.ui.views.customer.widgets.customer_history_widget import CustomerHistoryWidget
        self.info_tab = CustomerInfoWidget()
        self.finances_tab = CustomerBalanceWidget()
        self.payments_tab = CustomerPaymentsWidget()
        self.history_tab = CustomerHistoryWidget()
        self.details_tabs.addTab(self.info_tab, "Informations")
        self.details_tabs.addTab(self.finances_tab, "Finances")
        self.details_tabs.addTab(self.payments_tab, "Paiements")
        self.details_tabs.addTab(self.history_tab, "Historique client")
        main_layout.addWidget(self.details_tabs)

        # Tableau des clients en bas
        self.table_view = QTableView()
        self.table_view.setObjectName("customerTable")
        self.table_model = CustomerTableModel()
        from ...components.custom_filter_proxy_model import CustomFilterProxyModel
        self.proxy_model = CustomFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.table_view.setModel(self.proxy_model)

        # Configuration du tableau
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)

        # Configuration avancée du tableau pour une meilleure présentation
        header = self.table_view.horizontalHeader()

        # Ajuster automatiquement la largeur de chaque colonne selon son contenu
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # Étirer la dernière colonne pour utiliser tout l'espace restant
        header.setStretchLastSection(True)

        # Désactiver l'édition directe dans le tableau
        self.table_view.setEditTriggers(QTableView.EditTrigger.NoEditTriggers)

        # Améliorer l'apparence générale
        self.table_view.setShowGrid(True)

        # Style modernisé du tableau (identique à Réparation)
        self.table_view.setStyleSheet(
            """
            QTableView {
                background: #fff;
                border-radius: 12px;
                border: 1.5px solid #e0e0e0;
                font-size: 15px;
                color: #222;
                selection-background-color: #1976D2;
                selection-color: #fff;
                alternate-background-color: #f5f7fa;
                gridline-color: #e0e0e0;
                qproperty-alignment: 'AlignCenter';
            }
            QHeaderView::section {
                background: #f5f7fa;
                color: #1976D2;
                font-weight: bold;
                font-size: 15px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                border-radius: 8px 8px 0 0;
                padding: 8px 0;
                qproperty-alignment: 'AlignCenter';
                text-align: center;
            }
            QTableView::item { text-align: center; }
            QTableView::item:selected { background-color: #1976D2; color: #fff; font-weight: bold; }
            QTableView::item:hover { background-color: #e3f0fc; }
            """
        )

        # Activer les lignes alternées pour une meilleure lisibilité
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setMinimumHeight(180)
        self.table_view.setMaximumHeight(350)

        main_layout.addWidget(self.table_view)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)
        self.loading_overlay.hide()

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_button.clicked.connect(self.show_add_dialog)
        self.edit_button.clicked.connect(self.edit_selected_customer)
        self.delete_button.clicked.connect(self.delete_customer)
        self.export_button.clicked.connect(self.export_customers)

        self.search_bar.textChanged.connect(self.filter_customers)

        self.table_view.doubleClicked.connect(self.show_edit_dialog)
        self.table_view.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table_view.customContextMenuRequested.connect(self.show_context_menu)
        # Mettre à jour le panneau de détails selon la sélection
        self.table_view.selectionModel().selectionChanged.connect(self.update_details_panel)

    async def load_data(self):
        """Charge les données des clients"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            print("CustomerView: Chargement des données...")
            await self.table_model.load_data()
            print(f"CustomerView: {self.table_model.rowCount()} clients chargés")
        except Exception as e:
            print(f"CustomerView: Erreur lors du chargement des données: {e}")
        finally:
            self.loading_overlay.hide()

    def filter_customers(self):
        """Applique le filtre de recherche sur le tableau"""
        search_text = self.search_bar.text()
        self.proxy_model.set_filter_text(search_text)

    def apply_status_filter(self):
        """Filtre par statut client (à jour / non à jour)"""
        # up_to_date: current_balance <= 0
        # not_up_to_date: current_balance > 0
        selected = self.status_filter.currentData()
        # Nous utilisons set_filters avec une clé dédiée que filterAcceptsRow saura interpréter
        self.proxy_model.set_filters({'customer_status': selected})

    def show_add_dialog(self):
        """Affiche la boîte de dialogue d'ajout de client"""
        dialog = CustomerDialog(self)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

    def edit_selected_customer(self):
        """Édite le client sélectionné"""
        customer_id = self.get_selected_customer_id()
        if not customer_id:
            return

        dialog = CustomerDialog(self, customer_id=customer_id)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", "Le client a été modifié avec succès.")

    def show_edit_dialog(self, index):
        """Affiche la boîte de dialogue d'édition de client (appelé lors du double-clic)"""
        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(index)
        customer_id = self.table_model.get_customer_id(source_index.row())

        dialog = CustomerDialog(self, customer_id=customer_id)
        if dialog.exec():
            # Utiliser QTimer pour exécuter la méthode load_data dans le thread principal
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, self._load_data_wrapper)

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", "Le client a été modifié avec succès.")

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Exécuter la coroutine dans la boucle
            loop.run_until_complete(self.load_data())

            # Après chargement, auto-sélectionner la première ligne pour initialiser les onglets
            try:
                if self.table_model.rowCount() > 0:
                    self.table_view.selectRow(0)
                    # Forcer la mise à jour des onglets de détails
                    sel = self.table_view.selectionModel().selection()
                    self.update_details_panel(sel, sel)
            except Exception:
                pass

            # Fermer la boucle après utilisation
            loop.close()

        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()

    def on_selection_changed(self, selected, deselected):
        """Gère le changement de sélection dans le tableau"""
        # Activer/désactiver les boutons en fonction de la sélection
        has_selection = len(self.table_view.selectionModel().selectedRows()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)

    def update_details_panel(self, selected, deselected):
        """Met à jour les onglets de détails en fonction du client sélectionné"""
        customer_id = self.get_selected_customer_id()
        if not customer_id:
            # Aucun client sélectionné, remettre à zéro
            for w in (getattr(self, 'finances_tab', None), getattr(self, 'info_tab', None), getattr(self, 'payments_tab', None), getattr(self, 'history_tab', None)):
                if hasattr(w, 'clear'):
                    w.clear()
            return
        # Mettre à jour les onglets
        if hasattr(self, 'finances_tab') and hasattr(self.finances_tab, 'set_customer'):
            self.finances_tab.set_customer(customer_id)
        if hasattr(self, 'info_tab') and hasattr(self.info_tab, 'set_customer'):
            self.info_tab.set_customer(customer_id)
        if hasattr(self, 'payments_tab') and hasattr(self.payments_tab, 'set_customer'):
            self.payments_tab.set_customer(customer_id)
        if hasattr(self, 'history_tab') and hasattr(self.history_tab, 'set_customer'):
            self.history_tab.set_customer(customer_id)

    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        # Vérifier si une ligne est sélectionnée
        indexes = self.table_view.selectionModel().selectedRows()
        if not indexes:
            return

        # Créer le menu contextuel
        menu = QMenu(self)

        # Ajouter les actions
        edit_action = QAction(QIcon("app/ui/resources/icons/edit.svg"), "Modifier", self)
        edit_action.triggered.connect(lambda: self.show_edit_dialog(indexes[0]))
        menu.addAction(edit_action)

        menu.addSeparator()

        delete_action = QAction(QIcon("app/ui/resources/icons/delete.svg"), "Supprimer", self)
        delete_action.triggered.connect(self.delete_customer)
        menu.addAction(delete_action)

        # Afficher le menu
        menu.exec(QCursor.pos())

    def get_selected_customer_id(self):
        """Récupère l'ID du client sélectionné"""
        indexes = self.table_view.selectionModel().selectedRows()
        if not indexes:
            return None

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        return self.table_model.get_customer_id(source_index.row())

    def delete_customer(self):
        """Supprime (désactive) le client sélectionné"""
        customer_id = self.get_selected_customer_id()
        if not customer_id:
            return

        # Demander confirmation
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Êtes-vous sûr de vouloir supprimer ce client ? Cette action ne peut pas être annulée.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Désactiver le client
        try:
            # Créer une nouvelle boucle d'événements
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Créer une nouvelle session pour éviter les conflits
            from app.utils.database import SessionLocal
            db = SessionLocal()
            service = CustomerService(db)

            # Désactiver le client
            loop.run_until_complete(service.deactivate_customer(customer_id))

            # Fermer la session
            db.close()

            # Fermer la boucle après utilisation
            loop.close()

            # Actualiser les données
            self._load_data_wrapper()

            QMessageBox.information(self, "Succès", "Le client a été supprimé avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du client: {str(e)}")
            print(f"Erreur lors de la suppression du client: {e}")
            import traceback
            traceback.print_exc()

    # La gestion financière par bouton est retirée; les opérations financières seront disponibles dans l'onglet "Finances" du panneau de détails.

    def export_customers(self):
        """Exporte les données des clients"""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox
        import os
        import pandas as pd

        # Demander le format d'export
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("Format d'exportation")
        msg_box.setText("Choisissez le format d'exportation :")

        excel_button = msg_box.addButton("Excel (.xlsx)", QMessageBox.ButtonRole.YesRole)
        csv_button = msg_box.addButton("CSV (.csv)", QMessageBox.ButtonRole.NoRole)
        cancel_button = msg_box.addButton("Annuler", QMessageBox.ButtonRole.RejectRole)

        msg_box.exec()
        clicked_button = msg_box.clickedButton()

        if clicked_button == cancel_button:
            return

        # Déterminer le format choisi
        export_to_excel_format = (clicked_button == excel_button)

        if export_to_excel_format:
            self._export_customers_to_excel()
        else:
            self._export_customers_to_csv()

    def _export_customers_to_csv(self):
        """Exporte les clients au format CSV"""
        from PyQt6.QtWidgets import QFileDialog
        import os

        # Demander à l'utilisateur où enregistrer le fichier
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exporter les clients (CSV)",
            os.path.expanduser("~/Documents/Clients.csv"),
            "Fichiers CSV (*.csv)"
        )

        if file_path:
            try:
                # Exporter les données
                export_table_data(self.table_view, file_path)
                QMessageBox.information(
                    self,
                    "Export réussi",
                    f"Les clients ont été exportés avec succès vers :\n{file_path}"
                )
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Erreur d'exportation",
                    f"Erreur lors de l'exportation CSV : {str(e)}"
                )

    def _export_customers_to_excel(self):
        """Exporte les clients au format Excel avec plusieurs onglets"""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox
        import os
        import pandas as pd
        from datetime import datetime

        # Demander à l'utilisateur où enregistrer le fichier
        default_name = f"Clients_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exporter les clients (Excel)",
            os.path.expanduser(f"~/Documents/{default_name}"),
            "Fichiers Excel (*.xlsx)"
        )

        if not file_path:
            return

        try:
            # Préparer les données pour l'export Excel
            customer_data = self._prepare_customer_data_for_excel()

            # Exporter vers Excel
            if export_to_excel(customer_data, file_path, self):
                QMessageBox.information(
                    self,
                    "Export réussi",
                    f"Les clients ont été exportés avec succès vers :\n{file_path}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur d'exportation",
                f"Erreur lors de l'exportation Excel : {str(e)}"
            )

    def _prepare_customer_data_for_excel(self):
        """Prépare les données des clients pour l'export Excel"""
        import pandas as pd
        from PyQt6.QtCore import Qt

        # Récupérer le modèle de données
        model = self.table_view.model()
        if hasattr(model, 'sourceModel'):
            # Si c'est un proxy model, récupérer le modèle source
            source_model = model.sourceModel()
        else:
            source_model = model

        # Préparer les données principales des clients
        customers_data = []

        # Récupérer les en-têtes
        headers = []
        for i in range(source_model.columnCount()):
            header = source_model.headerData(i, Qt.Orientation.Horizontal, Qt.ItemDataRole.DisplayRole)
            headers.append(header if header else f"Colonne {i}")

        # Récupérer les données des lignes
        for row in range(source_model.rowCount()):
            row_data = {}
            for col in range(source_model.columnCount()):
                index = source_model.index(row, col)
                value = source_model.data(index, Qt.ItemDataRole.DisplayRole)
                row_data[headers[col]] = value if value is not None else ""
            customers_data.append(row_data)

        # Créer le DataFrame principal
        customers_df = pd.DataFrame(customers_data)

        # Préparer les statistiques de résumé
        summary_data = self._prepare_customer_summary_stats()
        summary_df = pd.DataFrame(summary_data)

        # Préparer les données par ville
        city_data = self._prepare_customer_city_breakdown(customers_data)
        city_df = pd.DataFrame(city_data)

        # Préparer les données par statut
        status_data = self._prepare_customer_status_breakdown(customers_data)
        status_df = pd.DataFrame(status_data)

        # Préparer l'analyse financière
        financial_data = self._prepare_customer_financial_analysis(customers_data)
        financial_df = pd.DataFrame(financial_data)

        # Retourner un dictionnaire avec tous les onglets
        return {
            "Clients": customers_df,
            "Résumé": summary_df,
            "Par Ville": city_df,
            "Par Statut": status_df,
            "Analyse Financière": financial_df
        }

    def _prepare_customer_summary_stats(self):
        """Prépare les statistiques de résumé des clients"""
        import pandas as pd
        from datetime import datetime

        try:
            # Récupérer les statistiques depuis le modèle ou calculer
            model = self.table_view.model()
            if hasattr(model, 'sourceModel'):
                source_model = model.sourceModel()
            else:
                source_model = model

            total_customers = source_model.rowCount()

            # Compter par ville (approximatif basé sur les données visibles)
            city_counts = {}
            active_count = 0
            total_credit_limit = 0
            total_balance = 0

            for row in range(source_model.rowCount()):
                # Ville (supposons colonne 7)
                city_index = source_model.index(row, 7)  # Colonne "Ville"
                city = source_model.data(city_index, Qt.ItemDataRole.DisplayRole)
                if city and city != "-":
                    city_counts[city] = city_counts.get(city, 0) + 1

                # Limite de crédit (supposons colonne 8)
                credit_index = source_model.index(row, 8)  # Colonne "Limite crédit"
                credit = source_model.data(credit_index, Qt.ItemDataRole.DisplayRole)
                if credit and str(credit).replace('.', '').replace(',', '').isdigit():
                    total_credit_limit += float(str(credit).replace(',', ''))

                # Solde actuel (supposons colonne 9)
                balance_index = source_model.index(row, 9)  # Colonne "Solde actuel"
                balance = source_model.data(balance_index, Qt.ItemDataRole.DisplayRole)
                if balance and str(balance).replace('.', '').replace(',', '').replace('-', '').isdigit():
                    total_balance += float(str(balance).replace(',', ''))

            # Préparer les données de résumé
            summary_data = [
                {"Métrique": "Nombre total de clients", "Valeur": total_customers},
                {"Métrique": "Date d'export", "Valeur": datetime.now().strftime("%d/%m/%Y %H:%M")},
                {"Métrique": "Villes représentées", "Valeur": len(city_counts)},
                {"Métrique": "Limite de crédit totale", "Valeur": f"{total_credit_limit:.2f} DA"},
                {"Métrique": "Solde total", "Valeur": f"{total_balance:.2f} DA"},
            ]

            # Ajouter les comptes par ville (top 5)
            sorted_cities = sorted(city_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            for city, count in sorted_cities:
                summary_data.append({
                    "Métrique": f"Clients à {city}",
                    "Valeur": count
                })

            return summary_data

        except Exception as e:
            print(f"Erreur lors de la préparation des statistiques: {e}")
            return [
                {"Métrique": "Nombre total de clients", "Valeur": "N/A"},
                {"Métrique": "Date d'export", "Valeur": datetime.now().strftime("%d/%m/%Y %H:%M")},
            ]

    def _prepare_customer_city_breakdown(self, customers_data):
        """Prépare la répartition des clients par ville"""
        try:
            city_counts = {}

            for customer in customers_data:
                city = customer.get("Ville", "Non spécifiée")
                if not city or city.strip() == "" or city == "-":
                    city = "Non spécifiée"
                city_counts[city] = city_counts.get(city, 0) + 1

            # Convertir en liste de dictionnaires pour DataFrame
            city_data = []
            for city, count in city_counts.items():
                percentage = (count / len(customers_data) * 100) if customers_data else 0
                city_data.append({
                    "Ville": city,
                    "Nombre de clients": count,
                    "Pourcentage": f"{percentage:.1f}%"
                })

            # Trier par nombre décroissant
            city_data.sort(key=lambda x: x["Nombre de clients"], reverse=True)

            return city_data

        except Exception as e:
            print(f"Erreur lors de la préparation des données par ville: {e}")
            return [{"Ville": "Erreur", "Nombre de clients": 0, "Pourcentage": "0%"}]

    def _prepare_customer_status_breakdown(self, customers_data):
        """Prépare la répartition des clients par statut financier"""
        try:
            status_counts = {"À jour": 0, "En retard": 0, "Non défini": 0}

            for customer in customers_data:
                # Analyser le solde pour déterminer le statut
                balance_str = str(customer.get("Solde actuel", "0"))
                try:
                    balance = float(balance_str.replace(',', '').replace(' DA', ''))
                    if balance <= 0:
                        status_counts["À jour"] += 1
                    else:
                        status_counts["En retard"] += 1
                except:
                    status_counts["Non défini"] += 1

            # Convertir en liste de dictionnaires pour DataFrame
            status_data = []
            for status, count in status_counts.items():
                percentage = (count / len(customers_data) * 100) if customers_data else 0
                status_data.append({
                    "Statut": status,
                    "Nombre de clients": count,
                    "Pourcentage": f"{percentage:.1f}%"
                })

            return status_data

        except Exception as e:
            print(f"Erreur lors de la préparation des données par statut: {e}")
            return [{"Statut": "Erreur", "Nombre de clients": 0, "Pourcentage": "0%"}]

    def _prepare_customer_financial_analysis(self, customers_data):
        """Prépare l'analyse financière des clients"""
        try:
            total_credit_limit = 0
            total_balance = 0
            clients_with_debt = 0
            clients_with_credit = 0
            max_debt = 0
            max_credit_limit = 0

            for customer in customers_data:
                # Analyser la limite de crédit
                credit_str = str(customer.get("Limite crédit", "0"))
                try:
                    credit = float(credit_str.replace(',', '').replace(' DA', ''))
                    total_credit_limit += credit
                    if credit > max_credit_limit:
                        max_credit_limit = credit
                    if credit > 0:
                        clients_with_credit += 1
                except:
                    pass

                # Analyser le solde
                balance_str = str(customer.get("Solde actuel", "0"))
                try:
                    balance = float(balance_str.replace(',', '').replace(' DA', ''))
                    total_balance += balance
                    if balance > 0:
                        clients_with_debt += 1
                        if balance > max_debt:
                            max_debt = balance
                except:
                    pass

            # Calculer les moyennes
            avg_credit_limit = total_credit_limit / len(customers_data) if customers_data else 0
            avg_balance = total_balance / len(customers_data) if customers_data else 0

            # Préparer les données d'analyse
            financial_data = [
                {"Métrique": "Limite de crédit totale", "Valeur": f"{total_credit_limit:.2f} DA"},
                {"Métrique": "Limite de crédit moyenne", "Valeur": f"{avg_credit_limit:.2f} DA"},
                {"Métrique": "Limite de crédit maximale", "Valeur": f"{max_credit_limit:.2f} DA"},
                {"Métrique": "Solde total", "Valeur": f"{total_balance:.2f} DA"},
                {"Métrique": "Solde moyen", "Valeur": f"{avg_balance:.2f} DA"},
                {"Métrique": "Dette maximale", "Valeur": f"{max_debt:.2f} DA"},
                {"Métrique": "Clients avec crédit", "Valeur": clients_with_credit},
                {"Métrique": "Clients endettés", "Valeur": clients_with_debt},
                {"Métrique": "Taux d'endettement", "Valeur": f"{(clients_with_debt / len(customers_data) * 100):.1f}%" if customers_data else "0%"},
            ]

            return financial_data

        except Exception as e:
            print(f"Erreur lors de la préparation de l'analyse financière: {e}")
            return [{"Métrique": "Erreur", "Valeur": "N/A"}]

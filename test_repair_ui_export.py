#!/usr/bin/env python3
"""
Script de test pour vérifier l'interface utilisateur d'export des réparations
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

def test_repair_view_export_ui():
    """Test de l'interface utilisateur d'export des réparations"""
    
    print("=== TEST DE L'INTERFACE D'EXPORT DES RÉPARATIONS ===\n")
    
    # Créer l'application Qt
    app = QApplication(sys.argv)
    
    try:
        # Importer et créer la vue des réparations
        from app.ui.views.repair.repair_view import RepairView
        
        print("✅ RepairView importée avec succès")
        
        # Créer la vue
        repair_view = RepairView()
        print("✅ RepairView créée avec succès")
        
        # Vérifier que les boutons d'export existent
        if hasattr(repair_view, 'export_button'):
            print("✅ Bouton d'export principal trouvé")
        else:
            print("❌ Bouton d'export principal non trouvé")
        
        if hasattr(repair_view, 'fab_export'):
            print("✅ Bouton d'export flottant trouvé")
        else:
            print("❌ Bouton d'export flottant non trouvé")
        
        # Vérifier que les méthodes d'export existent
        methods_to_check = [
            'export_repairs',
            '_export_repairs_to_csv', 
            '_export_repairs_to_excel',
            '_prepare_repair_data_for_excel',
            '_prepare_repair_summary_stats',
            '_prepare_repair_status_breakdown',
            '_prepare_repair_technician_breakdown'
        ]
        
        print("\n📋 Vérification des méthodes d'export:")
        for method_name in methods_to_check:
            if hasattr(repair_view, method_name):
                print(f"   ✅ {method_name}")
            else:
                print(f"   ❌ {method_name}")
        
        # Vérifier que le tableau existe
        if hasattr(repair_view, 'table_view'):
            print("\n✅ TableView trouvée")
            
            # Vérifier le modèle
            if hasattr(repair_view, 'table_model'):
                print("✅ TableModel trouvé")
            else:
                print("❌ TableModel non trouvé")
        else:
            print("\n❌ TableView non trouvée")
        
        print("\n💡 Interface d'export des réparations prête!")
        print("🔧 Pour tester manuellement:")
        print("   1. Lancez l'application principale")
        print("   2. Allez dans le module Réparations")
        print("   3. Cliquez sur le bouton 'Exporter' ou le bouton flottant '⇩'")
        print("   4. Choisissez entre Excel (.xlsx) et CSV (.csv)")
        print("   5. Sélectionnez l'emplacement de sauvegarde")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de l'interface: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        app.quit()

def test_export_methods_callable():
    """Test que les méthodes d'export peuvent être appelées"""
    
    print("\n=== TEST D'APPEL DES MÉTHODES D'EXPORT ===\n")
    
    try:
        # Créer l'application Qt (nécessaire pour les widgets)
        app = QApplication(sys.argv)
        
        from app.ui.views.repair.repair_view import RepairView
        
        # Créer la vue
        repair_view = RepairView()
        
        # Tester la préparation des statistiques de résumé
        try:
            summary_stats = repair_view._prepare_repair_summary_stats()
            print("✅ _prepare_repair_summary_stats() fonctionne")
            print(f"   Nombre d'éléments de résumé: {len(summary_stats)}")
        except Exception as e:
            print(f"❌ Erreur dans _prepare_repair_summary_stats(): {e}")
        
        # Tester la préparation des données par statut avec des données fictives
        try:
            test_repairs_data = [
                {"Statut": "En cours", "Client": "Client A"},
                {"Statut": "Terminé", "Client": "Client B"},
                {"Statut": "En cours", "Client": "Client C"},
            ]
            status_breakdown = repair_view._prepare_repair_status_breakdown(test_repairs_data)
            print("✅ _prepare_repair_status_breakdown() fonctionne")
            print(f"   Nombre de statuts: {len(status_breakdown)}")
        except Exception as e:
            print(f"❌ Erreur dans _prepare_repair_status_breakdown(): {e}")
        
        # Tester la préparation des données par technicien avec des données fictives
        try:
            test_repairs_data = [
                {"Technicien": "Tech A", "Client": "Client A"},
                {"Technicien": "Tech B", "Client": "Client B"},
                {"Technicien": "Tech A", "Client": "Client C"},
            ]
            technician_breakdown = repair_view._prepare_repair_technician_breakdown(test_repairs_data)
            print("✅ _prepare_repair_technician_breakdown() fonctionne")
            print(f"   Nombre de techniciens: {len(technician_breakdown)}")
        except Exception as e:
            print(f"❌ Erreur dans _prepare_repair_technician_breakdown(): {e}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des méthodes: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale de test"""
    
    print("🧪 TEST DE L'INTERFACE D'EXPORT EXCEL DES RÉPARATIONS\n")
    
    # Test de l'interface utilisateur
    ui_ok = test_repair_view_export_ui()
    
    # Test des méthodes d'export
    methods_ok = test_export_methods_callable()
    
    # Résumé
    print("\n" + "="*50)
    print("RÉSUMÉ DES TESTS:")
    print(f"✅ Interface utilisateur: {'OK' if ui_ok else 'ÉCHEC'}")
    print(f"✅ Méthodes d'export: {'OK' if methods_ok else 'ÉCHEC'}")
    
    all_ok = ui_ok and methods_ok
    
    if all_ok:
        print("\n🎉 TOUS LES TESTS D'INTERFACE SONT PASSÉS!")
        print("📊 La fonctionnalité d'export Excel est prête à être utilisée.")
        print("\n📋 FONCTIONNALITÉS DISPONIBLES:")
        print("   • Export CSV (format existant)")
        print("   • Export Excel avec multiple onglets:")
        print("     - Réparations (données complètes)")
        print("     - Résumé (statistiques générales)")
        print("     - Par Statut (répartition par statut)")
        print("     - Par Technicien (répartition par technicien)")
    else:
        print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus avant d'utiliser la fonctionnalité.")
    
    return all_ok

if __name__ == "__main__":
    main()

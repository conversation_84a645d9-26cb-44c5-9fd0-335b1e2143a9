from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableView,
    QPushButton, QLineEdit, QComboBox, QLabel,
    QStackedWidget, QFrame, QTabWidget, QMessageBox,
    QMenu, QDialog, QSplitter, QInputDialog,
    QGraphicsOpacityEffect, QGraphicsDropShadowEffect
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve, QSize
from PyQt6.QtGui import QIcon, QAction, QCursor
import asyncio
import os

from .repair_table_model import RepairTableModel
from .dialogs.repair_dialog import RepairDialog
from .dialogs.payment_dialog import PaymentDialog
from .dialogs.invoice_dialog import InvoiceDialog
from .dialogs.used_parts_dialog import UsedPartsDialog
from .widgets.financial_summary_widget import FinancialSummaryWidget
from ...components.custom_widgets import <PERSON>Bar, FilterComboBox, LoadingOverlay
from ...components.custom_filter_proxy_model import CustomFilterProxyModel
from app.core.models.repair import RepairStatus, RepairPriority, PaymentStatus
from app.utils.export_utils import export_table_data, export_to_excel
from app.utils.config import get_settings
from app.ui.utils.dialog_utils import DialogUtils
from app.ui.utils.async_runner import AsyncRunner

class RepairView(QWidget):
    def __init__(self):
        super().__init__()
        self._fab_buttons = []  # store floating buttons for repositioning
        self.setup_ui()
        self.setup_connections()
        # Use QTimer to properly schedule the async load
        QTimer.singleShot(0, self._init_data)
        self._permissions_applied = False

    def apply_permissions(self, auth_controller):
        """Active/désactive le bouton Nouvelle Réparation selon les permissions utilisateur."""
        if not hasattr(auth_controller, 'has_permission'):
            return
        self.add_button.setEnabled(auth_controller.has_permission('repair.create'))
        self._permissions_applied = True

    def _init_data(self):
        """Initialize data loading"""
        # Utiliser notre wrapper pour exécuter load_data de manière asynchrone
        self._load_data_wrapper()

    def _load_data_wrapper(self):
        """Wrapper pour exécuter load_data de manière asynchrone"""
        AsyncRunner.run_async(self.load_data(), error_message="Erreur lors du chargement des données", parent=self)

    def on_scan_barcode(self):
        """Placeholder: Ouverture future du scanner code-barres"""
        QMessageBox.information(self, "Scanner code-barres", "La fonctionnalité de scan sera ajoutée prochainement.")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)
        # Supprimer le header texte pour cohérence avec Inventaire
        self._setup_toolbar(main_layout)
        self._setup_filters(main_layout)
        self._setup_main_content(main_layout)
        self._setup_loading_overlay()
        # Boutons flottants et améliorations "material"
        self._create_floating_buttons()
        try:
            self._fade_in_widget(self, duration=180)
        except Exception:
            pass

    # Header texte supprimé pour correspondre au design de l'inventaire

    def _setup_toolbar(self, main_layout):
        # En-tête modernisé avec style "material"
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: #fff;
                border-bottom: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px 18px 12px 18px;
                margin-bottom: 8px;
            }
        """)
        header_frame.setMinimumHeight(70)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setSpacing(18)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Style des boutons identique à l'inventaire (sans décalage vertical)
        button_style = (
            "QPushButton { background: #f5f7fa; color: #1976D2; border: 1px solid #e0e0e0; border-radius: 6px; padding: 8px 18px; font-weight: 600; } "
            "QPushButton:hover { background: #e3f0fc; color: #1565c0; border: 1px solid #90caf9; } "
            "QPushButton:pressed { background: #bbdefb; color: #0d47a1; } "
            "QPushButton:disabled { background: #f5f5f5; color: #bdbdbd; border: 1px solid #eeeeee; } "
        )

        # Taille unifiée des boutons de la barre d'outils
        toolbar_btn_height = 36
        toolbar_icon_size = QSize(18, 18)

        # Bouton: Nouvelle Réparation
        self.add_button = QPushButton("Nouvelle Réparation")
        self.add_button.setIcon(QIcon("app/ui/resources/icons/add.svg"))
        self.add_button.setStyleSheet(button_style)
        self.add_button.setFixedHeight(toolbar_btn_height)
        self.add_button.setIconSize(toolbar_icon_size)
        header_layout.addWidget(self.add_button)

        # Bouton: Scanner code-barres (fonction à implémenter)
        self.scan_button = QPushButton("Scanner code-barres")
        self.scan_button.setIcon(QIcon("app/ui/resources/icons/barcode.svg"))
        self.scan_button.setStyleSheet(button_style)
        self.scan_button.setToolTip("Scanner un code-barres (à venir)")
        self.scan_button.setFixedHeight(toolbar_btn_height)
        self.scan_button.setIconSize(toolbar_icon_size)
        header_layout.addWidget(self.scan_button)

        # Bouton: Exporter
        self.export_button = QPushButton("Exporter")
        self.export_button.setIcon(QIcon("app/ui/resources/icons/export.svg"))
        self.export_button.setStyleSheet(button_style)
        self.export_button.setFixedHeight(toolbar_btn_height)
        self.export_button.setIconSize(toolbar_icon_size)
        header_layout.addWidget(self.export_button)

        # Étirement après les boutons pour les ancrer à gauche
        header_layout.addStretch(1)

        main_layout.addWidget(header_frame)

    def _setup_filters(self, main_layout):
        filters_layout = QHBoxLayout()
        filters_layout.setSpacing(14)
        filters_layout.setContentsMargins(0, 0, 0, 0)

        # Barre de recherche (identique inventaire)
        self.search_bar = SearchBar("Rechercher une réparation...")
        self.search_bar.setFixedHeight(36)
        self.search_bar.setStyleSheet(
            """
            QLineEdit {
                background: #f5f7fa;
                border: 1.5px solid #e0e0e0;
                border-radius: 18px;
                padding-left: 36px;
                font-size: 15px;
                color: #222;
            }
            QLineEdit:focus {
                border: 1.5px solid #1976D2;
                background: #fff;
            }
            """
        )

        # Style des ComboBox identique inventaire
        combo_style = (
            "QComboBox { background: #f5f7fa; border: 1.5px solid #e0e0e0; border-radius: 16px; padding: 6px 24px 6px 14px; font-size: 15px; color: #1976D2; min-width: 120px; } "
            "QComboBox:focus { border: 1.5px solid #1976D2; background: #fff; } "
            "QComboBox::drop-down { border: none; } "
            "QComboBox QAbstractItemView { background: #fff; border-radius: 8px; } "
        )

        filters_layout.addWidget(self.search_bar, stretch=2)

        self.status_filter = FilterComboBox("Statut")
        self.status_filter.setStyleSheet(combo_style)
        self.status_filter.setFixedHeight(36)
        filters_layout.addWidget(self.status_filter, stretch=1)

        self.priority_filter = FilterComboBox("Priorité")
        self.priority_filter.setStyleSheet(combo_style)
        self.priority_filter.setFixedHeight(36)
        filters_layout.addWidget(self.priority_filter, stretch=1)

        self.technician_filter = FilterComboBox("Technicien")
        self.technician_filter.setStyleSheet(combo_style)
        self.technician_filter.setFixedHeight(36)
        filters_layout.addWidget(self.technician_filter, stretch=1)

        self.payment_status_filter = FilterComboBox("Statut de paiement")
        self.payment_status_filter.setStyleSheet(combo_style)
        self.payment_status_filter.setFixedHeight(36)
        filters_layout.addWidget(self.payment_status_filter, stretch=1)

        main_layout.addLayout(filters_layout)

    def _setup_main_content(self, main_layout):
        self.splitter = QSplitter(Qt.Orientation.Vertical)
        # Détails AU-DESSUS du tableau
        details_widget = self._create_details_widget()
        table_widget = self._create_table_widget()
        self.splitter.addWidget(details_widget)
        self.splitter.addWidget(table_widget)
        # Plus d'espace pour les détails
        self.splitter.setSizes([int(self.height() * 0.55), int(self.height() * 0.45)])
        main_layout.addWidget(self.splitter)

        # Animation d'apparition douce pour les détails
        try:
            self._fade_in_widget(details_widget)
        except Exception:
            pass

    def _create_table_widget(self):
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)
        self.table_view = QTableView()
        self.table_view.setObjectName("repairTable")
        self.table_model = RepairTableModel()
        self.proxy_model = CustomFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.table_view.setModel(self.proxy_model)
        # Alignement et style comme Inventaire
        try:
            from PyQt6.QtWidgets import QHeaderView
            self.table_view.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        except Exception:
            pass
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.horizontalHeader().setStretchLastSection(True)
        self.table_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        # Style modernisé du tableau (identique inventaire, mais neutre)
        self.table_view.setStyleSheet(
            """
            QTableView {
                background: #fff;
                border-radius: 12px;
                border: 1.5px solid #e0e0e0;
                font-size: 15px;
                color: #222;
                selection-background-color: #1976D2;
                selection-color: #fff;
                alternate-background-color: #f5f7fa;
                gridline-color: #e0e0e0;
                qproperty-alignment: 'AlignCenter';
            }
            QHeaderView::section {
                background: #f5f7fa;
                color: #1976D2;
                font-weight: bold;
                font-size: 15px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                border-radius: 8px 8px 0 0;
                padding: 8px 0;
                qproperty-alignment: 'AlignCenter';
                text-align: center;
            }
            QTableView::item { text-align: center; }
            QTableView::item:selected { background-color: #1976D2; color: #fff; font-weight: bold; }
            QTableView::item:hover { background-color: #e3f0fc; }
            """
        )
        # Hauteur similaire inventaire
        self.table_view.setMinimumHeight(180)
        self.table_view.setMaximumHeight(350)
        table_layout.addWidget(self.table_view)
        return table_widget

    def _create_details_widget(self):
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        details_layout.setContentsMargins(0, 0, 0, 0)
        self.details_tabs = QTabWidget()
        self.details_tabs.setObjectName("repairDetailsTabs")
        # Forcer un fond visible pour la pile interne des onglets afin d'éviter le rendu blanc
        try:
            self.details_tabs.setStyleSheet("QTabWidget::pane { background: #FFFFFF; } QStackedWidget, QStackedWidget > QWidget { background: #FFFFFF; color: #212121; }")
        except Exception:
            pass

        from .widgets.repair_details_widget import RepairDetailsWidget
        self.repair_details_widget = RepairDetailsWidget()
        self.details_tabs.addTab(self.repair_details_widget, "Informations")
        from .widgets.used_parts_widget import UsedPartsWidget
        self.used_parts_widget = UsedPartsWidget()
        self.details_tabs.addTab(self.used_parts_widget, "Pièces utilisées")
        self.financial_summary_widget = FinancialSummaryWidget()
        self.details_tabs.addTab(self.financial_summary_widget, "Finances")
        from .widgets.payments_widget import PaymentsWidget
        self.payments_widget = PaymentsWidget()
        self.details_tabs.addTab(self.payments_widget, "Paiements")
        from .widgets.repair_status_widget import RepairStatusWidget
        self.status_widget = RepairStatusWidget()
        self.details_tabs.addTab(self.status_widget, "Statut")
        self.status_widget.status_changed.connect(self.on_status_changed)
        from .widgets.repair_photos_widget import RepairPhotosWidget
        self.photos_widget = RepairPhotosWidget()
        self.details_tabs.addTab(self.photos_widget, "Photos")
        from .widgets.repair_notes_widget import RepairNotesWidget
        self.notes_widget = RepairNotesWidget()
        self.details_tabs.addTab(self.notes_widget, "Notes techniques")
        
        # Désactiver l'animation de changement d'onglet pour éviter le contenu invisible
        try:
            # Si déjà connecté ailleurs, on pourrait déconnecter, sinon ne rien faire
            try:
                self.details_tabs.currentChanged.disconnect()
            except Exception:
                pass
        except Exception:
            pass
        
        details_layout.addWidget(self.details_tabs)

        # Pas d'animation d'apparition pour éviter les effets d'opacité
        return details_widget

    def _setup_loading_overlay(self):
        self.loading_overlay = LoadingOverlay(self)
        try:
            # Effet d'opacité pour transitions
            effect = QGraphicsOpacityEffect(self)
            self.loading_overlay.setGraphicsEffect(effect)
        except Exception:
            pass

    def _fade_in_widget(self, widget, duration=200):
        """Désactivé: animations d'opacité neutralisées pour éviter le problème d'onglets vides."""
        try:
            # Au lieu d'animer l'opacité, on force un repaint immédiat
            widget.update()
            widget.repaint()
        except Exception:
            pass

    def _create_floating_buttons(self):
        """Crée des boutons flottants (FAB) pour actions rapides"""
        try:
            # Bouton flottant principal: Ajouter réparation
            self.fab_add = QPushButton("+")
            self.fab_add.setToolTip("Nouvelle Réparation")
            self.fab_add.setCursor(Qt.CursorShape.PointingHandCursor)
            self.fab_add.clicked.connect(self.show_add_repair_dialog)
            self._style_fab(self.fab_add, primary=True)
            self.fab_add.setParent(self)
            self.fab_add.raise_()
            self.fab_add.show()
            self._fab_buttons.append(self.fab_add)

            # Bouton flottant secondaire: Export
            self.fab_export = QPushButton("⇩")
            self.fab_export.setToolTip("Exporter")
            self.fab_export.setCursor(Qt.CursorShape.PointingHandCursor)
            self.fab_export.clicked.connect(self.export_repairs)
            self._style_fab(self.fab_export, primary=False)
            self.fab_export.setParent(self)
            self.fab_export.raise_()
            self.fab_export.show()
            self._fab_buttons.append(self.fab_export)

            # Positionner initialement
            self._position_fabs()
        except Exception:
            pass

    def _style_fab(self, button: QPushButton, primary: bool = False):
        """Style 'Material' pour FAB (ombre, rond, couleurs)"""
        size = 52 if primary else 44
        button.setFixedSize(size, size)
        button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {'#1976D2' if primary else '#455A64'};
                color: white;
                border: none;
                border-radius: {size//2}px;
                font: bold 20px 'Segoe UI';
            }}
            QPushButton:hover {{ background-color: {'#1565C0' if primary else '#37474F'}; }}
            QPushButton:pressed {{ background-color: {'#0D47A1' if primary else '#263238'}; }}
            """
        )
        # Ombre
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(18)
        shadow.setOffset(0, 6)
        shadow.setColor(Qt.GlobalColor.black)
        button.setGraphicsEffect(shadow)

    def _position_fabs(self):
        """Positionne les FAB en bas à droite avec un léger offset"""
        margin = 20
        x = self.width() - margin
        y = self.height() - margin
        if hasattr(self, 'fab_add') and self.fab_add:
            self.fab_add.move(x - self.fab_add.width(), y - self.fab_add.height())
        if hasattr(self, 'fab_export') and self.fab_export:
            self.fab_export.move(x - self.fab_export.width(), y - self.fab_add.height() - self.fab_export.height() - 12)

    def resizeEvent(self, event):
        super().resizeEvent(event)
        try:
            self._position_fabs()
        except Exception:
            pass

    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.add_button.clicked.connect(self.show_add_repair_dialog)
        self.export_button.clicked.connect(self.export_repairs)
        # placeholder: scanner code-barres (implémentation à venir)
        self.scan_button.clicked.connect(self.on_scan_barcode)

        self.search_bar.textChanged.connect(self.filter_repairs)
        self.status_filter.currentIndexChanged.connect(self.filter_repairs)
        self.priority_filter.currentIndexChanged.connect(self.filter_repairs)
        self.technician_filter.currentIndexChanged.connect(self.filter_repairs)
        self.payment_status_filter.currentIndexChanged.connect(self.filter_repairs)

        self.table_view.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.table_view.doubleClicked.connect(self.show_edit_repair_dialog)
        self.table_view.customContextMenuRequested.connect(self.show_context_menu)

    async def load_data(self):
        """Charge les données des réparations"""
        self.loading_overlay.show()
        try:
            # Charger les données du tableau
            await self.table_model.load_data()

            # Charger les filtres
            await self.load_filters()

            # Ajuster les colonnes
            self.table_view.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
        finally:
            self.loading_overlay.hide()

    async def load_filters(self):
        """Charge les données des filtres"""
        self._load_status_filter()
        self._load_priority_filter()
        await self._load_technician_filter()
        self._load_payment_status_filter()

    # Centralized display helpers
    def get_status_display(self, status):
        from app.ui.utils.display_maps import status_label
        return status_label(status)

    def get_priority_display(self, priority):
        from app.ui.utils.display_maps import priority_label
        return priority_label(priority)

    def get_payment_status_display(self, payment_status):
        from app.ui.utils.display_maps import payment_status_label
        return payment_status_label(payment_status)

    def _load_status_filter(self):
        self.status_filter.clear()
        self.status_filter.addItem("Tous les statuts", None)
        # Use a set to avoid duplicate labels (e.g., waiting_parts vs waiting_for_parts)
        seen_labels = set()
        # N'afficher que le sous-ensemble demandé et dans l'ordre
        ordered_statuses = [
            RepairStatus.PENDING,
            RepairStatus.IN_PROGRESS,
            RepairStatus.WAITING_FOR_PARTS,
            RepairStatus.ON_HOLD,
            RepairStatus.COMPLETED,
            RepairStatus.CANCELLED,
        ]
        for status in ordered_statuses:
            label = self.get_status_display(status)
            # Fusionner les deux variantes waiting_parts/waiting_for_parts dans le même filtre
            if status == RepairStatus.WAITING_FOR_PARTS:
                self.status_filter.addItem(label, (RepairStatus.WAITING_PARTS.value, RepairStatus.WAITING_FOR_PARTS.value))
            else:
                self.status_filter.addItem(label, status.value)

    def _load_priority_filter(self):
        self.priority_filter.clear()
        self.priority_filter.addItem("Toutes les priorités", None)
        for priority in RepairPriority:
            self.priority_filter.addItem(self.get_priority_display(priority), priority.value)

    async def _load_technician_filter(self):
        self.technician_filter.clear()
        self.technician_filter.addItem("Tous les techniciens", None)
        technicians = await self.get_technicians()
        for technician in technicians:
            display_name = f"{technician.first_name} {technician.last_name}" if hasattr(technician, 'first_name') else (
                technician.email if hasattr(technician, 'email') else str(technician.id)
            )
            self.technician_filter.addItem(display_name, technician.id)

    def _load_payment_status_filter(self):
        self.payment_status_filter.clear()
        self.payment_status_filter.addItem("Tous les statuts", None)
        for status in PaymentStatus:
            self.payment_status_filter.addItem(self.get_payment_status_display(status), status.value)

    def filter_repairs(self):
        """Applique les filtres sur le tableau des réparations"""
        search_text = self.search_bar.text()
        status = self.status_filter.currentData()
        priority = self.priority_filter.currentData()
        technician = self.technician_filter.currentData()
        payment_status = self.payment_status_filter.currentData()

        # Appliquer les filtres au modèle proxy
        self.proxy_model.set_filters({
            'search': search_text,
            'status': status,
            'priority': priority,
            'technician_id': technician,
            'payment_status': payment_status
        })

    def on_selection_changed(self, selected, _):
        """Gère le changement de sélection dans le tableau"""
        indexes = selected.indexes()
        if indexes:
            # Convertir l'index du modèle proxy en index du modèle source
            source_index = self.proxy_model.mapToSource(indexes[0])
            repair_id = self.table_model.get_repair_id(source_index.row())

            # Charger les détails de la réparation de manière asynchrone
            AsyncRunner.run_async(self.load_repair_details(repair_id), error_message="Erreur lors du chargement des détails", parent=self)
        else:
            # Effacer les détails
            self.repair_details_widget.clear()
            self.used_parts_widget.clear()
            self.financial_summary_widget.clear()
            self.payments_widget.clear()
            self.status_widget.clear()
            self.photos_widget.clear()
            self.notes_widget.clear()

    def on_status_changed(self, new_status):
        """Gère le changement de statut d'une réparation"""
        try:
            # Obtenir l'ID de la réparation actuellement sélectionnée
            indexes = self.table_view.selectedIndexes()
            if not indexes:
                return

            source_index = self.proxy_model.mapToSource(indexes[0])
            repair_id = self.table_model.get_repair_id(source_index.row())

            # Rafraîchir uniquement la réparation modifiée dans le modèle de table
            self._refresh_repair_in_table(repair_id)

            # Recharger les détails de la réparation pour mettre à jour l'onglet Informations
            self._load_repair_details_wrapper(repair_id)

        except Exception as e:
            print(f"Erreur lors de la mise à jour après changement de statut: {e}")
            import traceback
            traceback.print_exc()

    def _refresh_repair_in_table(self, repair_id):
        """Rafraîchit une réparation spécifique dans le tableau"""
        AsyncRunner.run_async(self.table_model.refresh_repair(repair_id), error_message="Erreur lors du rafraîchissement de la réparation", parent=self)



    async def load_repair_details(self, repair_id):
        """Charge les détails d'une réparation"""
        self.loading_overlay.show()
        try:
            # Charger la réparation
            repair = await self.get_repair(repair_id)

            # Mettre à jour les widgets de détails
            self.repair_details_widget.set_repair(repair)

            # Charger les pièces utilisées via le widget (et activer l'ajout)
            self.used_parts_widget.set_repair_id(repair_id)

            # Charger le résumé financier
            financial_summary = await self.get_financial_summary(repair_id)
            self.financial_summary_widget.set_data(financial_summary)

            # Charger les paiements
            # Laisser le widget charger ses paiements via son controller
            self.payments_widget.set_repair_id(repair_id)

            # Charger le statut avancé
            self.status_widget.set_repair_id(repair_id)

            # Charger les photos
            self.photos_widget.set_repair_id(repair_id)

            # Charger les notes techniques
            self.notes_widget.set_repair_id(repair_id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des détails: {str(e)}")
        finally:
            self.loading_overlay.hide()

    def show_context_menu(self, position):
        """Affiche le menu contextuel pour les réparations"""
        indexes = self.table_view.selectedIndexes()
        if not indexes:
            return

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        repair_id = self.table_model.get_repair_id(source_index.row())
        repair_status = self.table_model.get_repair_status(source_index.row())
        payment_status = self.table_model.get_payment_status(source_index.row())

        menu = QMenu(self)

        # Actions communes
        edit_action = QAction("Modifier", self)
        edit_action.triggered.connect(lambda: self.show_edit_repair_dialog(indexes[0]))
        menu.addAction(edit_action)

        # Actions spécifiques selon le statut
        if repair_status in [RepairStatus.COMPLETED, RepairStatus.DIAGNOSED]:
            invoice_action = QAction("Facturer", self)
            invoice_action.triggered.connect(lambda: self.show_invoice_dialog())
            menu.addAction(invoice_action)

        if repair_status in [RepairStatus.INVOICED, RepairStatus.PAID] or payment_status in [PaymentStatus.PARTIAL, PaymentStatus.PENDING]:
            payment_action = QAction("Enregistrer un paiement", self)
            payment_action.triggered.connect(lambda: self.show_payment_dialog())
            menu.addAction(payment_action)

        if repair_status in [RepairStatus.IN_PROGRESS, RepairStatus.DIAGNOSED]:
            parts_action = QAction("Ajouter des pièces utilisées", self)
            parts_action.triggered.connect(lambda: self.show_used_parts_dialog())
            menu.addAction(parts_action)

        # Séparateur
        menu.addSeparator()

        # Actions d'impression
        print_menu = QMenu("Imprimer", self)

        print_order_action = QAction("Ordre de réparation", self)
        print_order_action.triggered.connect(lambda: self.print_repair_order(repair_id))
        print_menu.addAction(print_order_action)

        # Ajouter l'option pour imprimer le reçu de dépôt
        print_deposit_receipt_action = QAction("Reçu de dépôt", self)
        print_deposit_receipt_action.triggered.connect(lambda: self.print_deposit_receipt(repair_id))
        print_menu.addAction(print_deposit_receipt_action)

        # Ajouter l'option pour imprimer le reçu de réparation
        if repair_status in [RepairStatus.COMPLETED, RepairStatus.INVOICED, RepairStatus.PAID]:
            print_repair_receipt_action = QAction("Reçu de réparation", self)
            print_repair_receipt_action.triggered.connect(lambda: self.print_repair_receipt(repair_id))
            print_menu.addAction(print_repair_receipt_action)

        if repair_status in [RepairStatus.INVOICED, RepairStatus.PAID]:
            print_invoice_action = QAction("Facture", self)
            print_invoice_action.triggered.connect(lambda: self.print_invoice(repair_id))
            print_menu.addAction(print_invoice_action)

        if payment_status in [PaymentStatus.PARTIAL, PaymentStatus.PAID]:
            print_receipt_action = QAction("Reçu de paiement", self)
            print_receipt_action.triggered.connect(lambda: self.print_receipt(repair_id))
            print_menu.addAction(print_receipt_action)

        menu.addMenu(print_menu)

        # Exécuter le menu
        menu.exec(self.table_view.viewport().mapToGlobal(position))

    def show_add_repair_dialog(self):
        """Affiche la boîte de dialogue d'ajout de réparation"""
        if DialogUtils.open_modal_dialog(RepairDialog, self):
            self._load_data_wrapper()

    def show_edit_repair_dialog(self, index):
        """Affiche la boîte de dialogue d'édition de réparation"""
        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(index)
        repair_id = self.table_model.get_repair_id(source_index.row())

        if DialogUtils.open_modal_dialog(RepairDialog, self, repair_id=repair_id):
            self._load_data_wrapper()

    def show_invoice_dialog(self):
        """Affiche la boîte de dialogue de facturation"""
        indexes = self.table_view.selectedIndexes()
        if not indexes:
            return

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        repair_id = self.table_model.get_repair_id(source_index.row())

        if DialogUtils.open_modal_dialog(InvoiceDialog, self, repair_id=repair_id):
            self._load_data_wrapper()
            # Recharger les détails de manière asynchrone
            self._load_repair_details_wrapper(repair_id)

    def show_payment_dialog(self):
        """Affiche la boîte de dialogue d'enregistrement de paiement"""
        indexes = self.table_view.selectedIndexes()
        if not indexes:
            return

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        repair_id = self.table_model.get_repair_id(source_index.row())

        dialog = PaymentDialog(self, repair_id=repair_id)
        if dialog.exec():
            # Actualiser les données après un paiement réussi
            self._load_data_wrapper()
            # Recharger les détails de manière asynchrone
            self._load_repair_details_wrapper(repair_id)

            # Afficher un message de confirmation
            from app.utils.event_bus import event_bus
            event_bus.show_success("Données actualisées après paiement")

    def show_used_parts_dialog(self):
        """Affiche la boîte de dialogue d'ajout de pièces utilisées"""
        indexes = self.table_view.selectedIndexes()
        if not indexes:
            return

        # Convertir l'index du modèle proxy en index du modèle source
        source_index = self.proxy_model.mapToSource(indexes[0])
        repair_id = self.table_model.get_repair_id(source_index.row())

        if DialogUtils.open_modal_dialog(UsedPartsDialog, self, repair_id=repair_id):
            # Recharger les données des réparations
            self._load_data_wrapper()
            # Recharger les détails de manière asynchrone
            self._load_repair_details_wrapper(repair_id)

    def _load_repair_details_wrapper(self, repair_id):
        """Wrapper pour exécuter load_repair_details de manière asynchrone via AsyncRunner"""
        AsyncRunner.run_async(self.load_repair_details(repair_id),
                              error_message="Erreur lors du chargement des détails",
                              parent=self)

    def export_repairs(self):
        """Exporte les données des réparations"""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox
        import os
        import pandas as pd

        # Demander le format d'export
        reply = QMessageBox.question(
            self,
            "Format d'exportation",
            "Choisissez le format d'exportation :",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel,
            QMessageBox.StandardButton.Yes
        )

        # Configurer les boutons personnalisés
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("Format d'exportation")
        msg_box.setText("Choisissez le format d'exportation :")

        excel_button = msg_box.addButton("Excel (.xlsx)", QMessageBox.ButtonRole.YesRole)
        csv_button = msg_box.addButton("CSV (.csv)", QMessageBox.ButtonRole.NoRole)
        cancel_button = msg_box.addButton("Annuler", QMessageBox.ButtonRole.RejectRole)

        msg_box.exec()
        clicked_button = msg_box.clickedButton()

        if clicked_button == cancel_button:
            return

        # Déterminer le format choisi
        export_to_excel_format = (clicked_button == excel_button)

        if export_to_excel_format:
            self._export_repairs_to_excel()
        else:
            self._export_repairs_to_csv()

    def _export_repairs_to_csv(self):
        """Exporte les réparations au format CSV"""
        from PyQt6.QtWidgets import QFileDialog
        import os

        # Demander à l'utilisateur où enregistrer le fichier
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exporter les réparations (CSV)",
            os.path.expanduser("~/Documents/Réparations.csv"),
            "Fichiers CSV (*.csv)"
        )

        if file_path:
            try:
                # Exporter les données
                export_table_data(self.table_view, file_path)
                QMessageBox.information(
                    self,
                    "Export réussi",
                    f"Les réparations ont été exportées avec succès vers :\n{file_path}"
                )
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Erreur d'exportation",
                    f"Erreur lors de l'exportation CSV : {str(e)}"
                )

    def _export_repairs_to_excel(self):
        """Exporte les réparations au format Excel avec plusieurs onglets"""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox
        import os
        import pandas as pd
        from datetime import datetime

        # Demander à l'utilisateur où enregistrer le fichier
        default_name = f"Réparations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exporter les réparations (Excel)",
            os.path.expanduser(f"~/Documents/{default_name}"),
            "Fichiers Excel (*.xlsx)"
        )

        if not file_path:
            return

        try:
            # Préparer les données pour l'export Excel
            repair_data = self._prepare_repair_data_for_excel()

            # Exporter vers Excel
            if export_to_excel(repair_data, file_path, self):
                QMessageBox.information(
                    self,
                    "Export réussi",
                    f"Les réparations ont été exportées avec succès vers :\n{file_path}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur d'exportation",
                f"Erreur lors de l'exportation Excel : {str(e)}"
            )

    def _prepare_repair_data_for_excel(self):
        """Prépare les données des réparations pour l'export Excel"""
        import pandas as pd
        from PyQt6.QtCore import Qt

        # Récupérer le modèle de données
        model = self.table_view.model()
        if hasattr(model, 'sourceModel'):
            # Si c'est un proxy model, récupérer le modèle source
            source_model = model.sourceModel()
        else:
            source_model = model

        # Préparer les données principales des réparations
        repairs_data = []

        # Récupérer les en-têtes
        headers = []
        for i in range(source_model.columnCount()):
            header = source_model.headerData(i, Qt.Orientation.Horizontal, Qt.ItemDataRole.DisplayRole)
            headers.append(header if header else f"Colonne {i}")

        # Récupérer les données des lignes
        for row in range(source_model.rowCount()):
            row_data = {}
            for col in range(source_model.columnCount()):
                index = source_model.index(row, col)
                value = source_model.data(index, Qt.ItemDataRole.DisplayRole)
                row_data[headers[col]] = value if value is not None else ""
            repairs_data.append(row_data)

        # Créer le DataFrame principal
        repairs_df = pd.DataFrame(repairs_data)

        # Préparer les statistiques de résumé
        summary_data = self._prepare_repair_summary_stats()
        summary_df = pd.DataFrame(summary_data)

        # Préparer les données par statut
        status_data = self._prepare_repair_status_breakdown(repairs_data)
        status_df = pd.DataFrame(status_data)

        # Préparer les données par technicien
        technician_data = self._prepare_repair_technician_breakdown(repairs_data)
        technician_df = pd.DataFrame(technician_data)

        # Retourner un dictionnaire avec tous les onglets
        return {
            "Réparations": repairs_df,
            "Résumé": summary_df,
            "Par Statut": status_df,
            "Par Technicien": technician_df
        }

    def _prepare_repair_summary_stats(self):
        """Prépare les statistiques de résumé des réparations"""
        import pandas as pd
        from datetime import datetime

        try:
            # Récupérer les statistiques depuis le modèle ou calculer
            model = self.table_view.model()
            if hasattr(model, 'sourceModel'):
                source_model = model.sourceModel()
            else:
                source_model = model

            total_repairs = source_model.rowCount()

            # Compter par statut (approximatif basé sur les données visibles)
            status_counts = {}
            for row in range(source_model.rowCount()):
                # Supposons que la colonne statut est à l'index 6 (basé sur les headers du modèle)
                status_index = source_model.index(row, 6)  # Colonne "Statut"
                status = source_model.data(status_index, Qt.ItemDataRole.DisplayRole)
                if status:
                    status_counts[status] = status_counts.get(status, 0) + 1

            # Préparer les données de résumé
            summary_data = [
                {"Métrique": "Nombre total de réparations", "Valeur": total_repairs},
                {"Métrique": "Date d'export", "Valeur": datetime.now().strftime("%d/%m/%Y %H:%M")},
            ]

            # Ajouter les comptes par statut
            for status, count in status_counts.items():
                summary_data.append({
                    "Métrique": f"Réparations {status}",
                    "Valeur": count
                })

            return summary_data

        except Exception as e:
            print(f"Erreur lors de la préparation des statistiques: {e}")
            return [
                {"Métrique": "Nombre total de réparations", "Valeur": "N/A"},
                {"Métrique": "Date d'export", "Valeur": datetime.now().strftime("%d/%m/%Y %H:%M")},
            ]

    def _prepare_repair_status_breakdown(self, repairs_data):
        """Prépare la répartition des réparations par statut"""
        try:
            status_counts = {}

            for repair in repairs_data:
                status = repair.get("Statut", "Non défini")
                status_counts[status] = status_counts.get(status, 0) + 1

            # Convertir en liste de dictionnaires pour DataFrame
            status_data = []
            for status, count in status_counts.items():
                percentage = (count / len(repairs_data) * 100) if repairs_data else 0
                status_data.append({
                    "Statut": status,
                    "Nombre": count,
                    "Pourcentage": f"{percentage:.1f}%"
                })

            # Trier par nombre décroissant
            status_data.sort(key=lambda x: x["Nombre"], reverse=True)

            return status_data

        except Exception as e:
            print(f"Erreur lors de la préparation des données par statut: {e}")
            return [{"Statut": "Erreur", "Nombre": 0, "Pourcentage": "0%"}]

    def _prepare_repair_technician_breakdown(self, repairs_data):
        """Prépare la répartition des réparations par technicien"""
        try:
            technician_counts = {}

            for repair in repairs_data:
                technician = repair.get("Technicien", "Non assigné")
                if not technician or technician.strip() == "":
                    technician = "Non assigné"
                technician_counts[technician] = technician_counts.get(technician, 0) + 1

            # Convertir en liste de dictionnaires pour DataFrame
            technician_data = []
            for technician, count in technician_counts.items():
                percentage = (count / len(repairs_data) * 100) if repairs_data else 0
                technician_data.append({
                    "Technicien": technician,
                    "Nombre de réparations": count,
                    "Pourcentage": f"{percentage:.1f}%"
                })

            # Trier par nombre décroissant
            technician_data.sort(key=lambda x: x["Nombre de réparations"], reverse=True)

            return technician_data

        except Exception as e:
            print(f"Erreur lors de la préparation des données par technicien: {e}")
            return [{"Technicien": "Erreur", "Nombre de réparations": 0, "Pourcentage": "0%"}]

    # Méthodes d'accès aux données (à implémenter avec votre service de réparation)
    async def get_repair(self, repair_id):
        """Récupère les détails d'une réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal

        db = SessionLocal()
        try:
            service = RepairService(db)
            return await service.get(repair_id)
        finally:
            db.close()

    async def get_used_parts(self, repair_id):
        """Récupère les pièces utilisées pour une réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal

        db = SessionLocal()
        try:
            service = RepairService(db)
            repair = await service.get(repair_id)
            return repair.used_parts if hasattr(repair, 'used_parts') else []
        finally:
            db.close()

    async def get_financial_summary(self, repair_id):
        """Récupère le résumé financier d'une réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal
        from decimal import Decimal

        db = SessionLocal()
        try:
            service = RepairService(db)
            repair = await service.get(repair_id)

            # Récupérer les paiements
            payments = getattr(repair, 'payments', [])

            # Calculer le total des paiements
            total_paid = sum(getattr(payment, 'amount', 0) for payment in payments)

            # Calculer le solde dû
            final_amount = getattr(repair, 'final_amount', 0)
            balance_due = final_amount - total_paid

            # Create a simple financial summary from the repair object
            return {
                'parts_cost': getattr(repair, 'parts_cost', 0),
                'labor_cost': getattr(repair, 'labor_cost', 0),
                'total_cost': getattr(repair, 'total_cost', 0),
                'tax_amount': getattr(repair, 'tax_amount', 0),
                'discount_amount': getattr(repair, 'discount_amount', 0),
                'final_amount': final_amount,
                'total_paid': total_paid,
                'balance_due': balance_due,
                'payment_status': getattr(repair, 'payment_status', 'PENDING'),
                'due_date': getattr(repair, 'due_date', None)
            }
        finally:
            db.close()

    async def get_payments(self, repair_id):
        """Récupère les paiements d'une réparation"""
        from app.core.services.repair_service import RepairService
        from app.utils.database import SessionLocal

        db = SessionLocal()
        try:
            service = RepairService(db)
            repair = await service.get(repair_id)
            # Return payments if available, otherwise empty list
            return getattr(repair, 'payments', [])
        finally:
            db.close()

    async def get_technicians(self):
        """Récupère la liste des techniciens"""
        from app.core.services.auth_service import UserService
        from app.core.models.config import SessionLocal

        db = SessionLocal()
        try:
            service = UserService(db)
            return await service.get_technicians()
        finally:
            db.close()

    # Méthodes d'impression
    def print_repair_order(self, repair_id):
        """Imprime l'ordre de réparation"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_repair_order(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression: {str(e)}")
        finally:
            loop.close()

    async def _print_repair_order(self, repair_id):
        """Version asynchrone de l'impression de l'ordre de réparation"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)
                # Check if the service has the generate_repair_order_pdf method
                if hasattr(service, 'generate_repair_order_pdf'):
                    pdf_path = await service.generate_repair_order_pdf(repair_id)

                    # Ouvrir le PDF
                    DialogUtils.open_pdf(pdf_path, parent=self)
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression: {str(e)}")

    def print_invoice(self, repair_id):
        """Imprime la facture"""
        asyncio.create_task(self._print_invoice(repair_id))

    async def _print_invoice(self, repair_id):
        """Version asynchrone de l'impression de la facture"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)
                # Check if the service has the generate_invoice method
                if hasattr(service, 'generate_invoice'):
                    result = await service.generate_invoice(repair_id)

                    # Ouvrir le PDF
                    if isinstance(result, dict) and "invoice_pdf" in result:
                        DialogUtils.open_pdf(result["invoice_pdf"], parent=self)
                    else:
                        QMessageBox.warning(self, "Avertissement", "Le format du résultat n'est pas celui attendu.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de facture n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression: {str(e)}")

    def print_receipt(self, repair_id):
        """Imprime le reçu de paiement"""
        asyncio.create_task(self._print_receipt(repair_id))

    async def _print_receipt(self, repair_id):
        """Version asynchrone de l'impression du reçu"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Check if the service has the necessary methods
                if hasattr(service, 'get') and hasattr(service, 'pdf_generator'):
                    # Obtenir la réparation
                    repair = await service.get(repair_id)

                    # Obtenir les paiements
                    payments = getattr(repair, 'payments', [])
                    if not payments:
                        QMessageBox.warning(self, "Avertissement", "Aucun paiement trouvé pour cette réparation.")
                        return

                    last_payment = payments[-1]

                    # Générer le reçu
                    pdf_path = await service.pdf_generator.generate_receipt(last_payment, repair)

                    # Ouvrir le PDF
                    DialogUtils.open_pdf(pdf_path, parent=self)
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression: {str(e)}")

    def print_deposit_receipt(self, repair_id):
        """Imprime le reçu de dépôt"""
        # Créer un menu contextuel pour choisir le type d'impression
        menu = QMenu(self)

        # Option pour prévisualiser le PDF
        preview_action = QAction("Prévisualiser", self)
        preview_action.triggered.connect(lambda: self._preview_deposit_receipt_pdf(repair_id))
        menu.addAction(preview_action)

        # Séparateur
        menu.addSeparator()

        # Option pour imprimer en PDF
        pdf_action = QAction("Imprimer en PDF", self)
        pdf_action.triggered.connect(lambda: self._print_deposit_receipt_pdf(repair_id))
        menu.addAction(pdf_action)

        # Option pour imprimer sur une imprimante thermique
        thermal_action = QAction("Imprimer sur imprimante thermique", self)
        thermal_action.triggered.connect(lambda: self._print_deposit_receipt_thermal(repair_id))
        menu.addAction(thermal_action)

        # Afficher le menu
        cursor_pos = QCursor.pos()
        menu.exec(cursor_pos)

    def print_repair_receipt(self, repair_id):
        """Imprime le reçu de réparation"""
        # Créer un menu contextuel pour choisir le type d'impression
        menu = QMenu(self)

        # Option pour prévisualiser le PDF
        preview_action = QAction("Prévisualiser", self)
        preview_action.triggered.connect(lambda: self._preview_repair_receipt_pdf(repair_id))
        menu.addAction(preview_action)

        # Séparateur
        menu.addSeparator()

        # Option pour imprimer en PDF
        pdf_action = QAction("Imprimer en PDF", self)
        pdf_action.triggered.connect(lambda: self._print_repair_receipt_pdf(repair_id))
        menu.addAction(pdf_action)

        # Option pour imprimer sur une imprimante thermique
        thermal_action = QAction("Imprimer sur imprimante thermique", self)
        thermal_action.triggered.connect(lambda: self._print_repair_receipt_thermal(repair_id))
        menu.addAction(thermal_action)

        # Afficher le menu
        cursor_pos = QCursor.pos()
        menu.exec(cursor_pos)

    def _print_deposit_receipt_pdf(self, repair_id):
        """Imprime le reçu de dépôt en PDF"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_deposit_receipt_pdf_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")
        finally:
            loop.close()

    def _print_deposit_receipt_thermal(self, repair_id):
        """Imprime le reçu de dépôt sur une imprimante thermique"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_deposit_receipt_thermal_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")
        finally:
            loop.close()

    async def _print_deposit_receipt_pdf_async(self, repair_id):
        """Version asynchrone de l'impression du reçu de dépôt en PDF"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Vérifier si le service a la méthode nécessaire
                if hasattr(service, 'print_repair_deposit_receipt'):
                    # Générer le reçu de dépôt
                    pdf_path = await service.print_repair_deposit_receipt(repair_id)

                    # Ouvrir le PDF
                    if pdf_path:
                        DialogUtils.open_pdf(pdf_path, parent=self)
                    else:
                        QMessageBox.warning(self, "Avertissement", "Impossible de générer le reçu de dépôt.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu de dépôt n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")

    async def _print_deposit_receipt_thermal_async(self, repair_id):
        """Version asynchrone de l'impression du reçu de dépôt sur une imprimante thermique"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.utils.escpos_printer import ESCPOSPrinter
            from app.ui.views.settings.dialogs.printer_config_dialog import PrinterConfigDialog

            # Vérifier si le module escpos est installé
            try:
                from app.utils.escpos_printer import ESCPOSPrinter
            except ImportError:
                import sys
                import subprocess
                QMessageBox.information(self, "Installation",
                    "Le module python-escpos est nécessaire pour l'impression thermique. Installation en cours...")
                try:
                    # Installer python-escpos
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-escpos>=3.0"])
                    # Réimporter après installation
                    from app.utils.escpos_printer import ESCPOSPrinter
                except Exception as install_error:
                    QMessageBox.critical(self, "Erreur d'installation",
                        f"Impossible d'installer python-escpos. Erreur : {str(install_error)}\n"
                        "Veuillez installer manuellement en exécutant :\n"
                        "pip install python-escpos>=3.0")
                    return

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Récupérer les données de la réparation
                repair_data = await service.get_repair_with_details(repair_id)
                if not repair_data:
                    QMessageBox.warning(self, "Avertissement", f"Aucune donnée trouvée pour la réparation #{repair_id}")
                    return

                # Récupérer la configuration des imprimantes
                printer_config = PrinterConfigDialog.get_printer_config()
                default_printer = PrinterConfigDialog.get_default_printer()

                if default_printer:
                    # Utiliser l'imprimante par défaut
                    printer_name = default_printer["name"]
                    connection_type = default_printer.get("connection_type", "windows")
                    connection_params = default_printer.get("connection_params", {})

                    # Créer l'imprimante ESC/POS
                    printer = ESCPOSPrinter(
                        printer_name=printer_name,
                        connection_type=connection_type,
                        connection_params=connection_params
                    )

                    # Imprimer le reçu
                    success = printer.print_repair_deposit_receipt(repair_data)

                    if success:
                        QMessageBox.information(self, "Impression", f"Le reçu de dépôt a été imprimé avec succès sur {printer_name}.")
                    else:
                        # Si l'impression échoue avec l'imprimante par défaut, demander à l'utilisateur de sélectionner une autre imprimante
                        QMessageBox.warning(
                            self,
                            "Avertissement",
                            f"Impossible d'imprimer le reçu de dépôt sur l'imprimante par défaut ({printer_name}). Veuillez sélectionner une autre imprimante."
                        )
                        self._print_deposit_receipt_thermal_with_selection(repair_data, printer_config)
                else:
                    # Aucune imprimante par défaut, demander à l'utilisateur de sélectionner une imprimante
                    self._print_deposit_receipt_thermal_with_selection(repair_data, printer_config)
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")

    def _print_repair_receipt_pdf(self, repair_id):
        """Imprime le reçu de réparation en PDF"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_repair_receipt_pdf_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")
        finally:
            loop.close()

    def _print_repair_receipt_thermal(self, repair_id):
        """Imprime le reçu de réparation sur une imprimante thermique"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._print_repair_receipt_thermal_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")
        finally:
            loop.close()

    def _preview_repair_receipt_pdf(self, repair_id):
        """Prévisualise le reçu de réparation en PDF"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._preview_repair_receipt_pdf_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation du reçu de réparation: {str(e)}")
        finally:
            loop.close()

    def _preview_deposit_receipt_pdf(self, repair_id):
        """Prévisualise le reçu de dépôt en PDF"""
        # Utiliser un wrapper pour exécuter la méthode asynchrone
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._preview_deposit_receipt_pdf_async(repair_id))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation du reçu de dépôt: {str(e)}")
        finally:
            loop.close()

    async def _preview_repair_receipt_pdf_async(self, repair_id):
        """Version asynchrone de la prévisualisation du reçu de réparation en PDF"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.ui.components.pdf_preview_dialog import PDFPreviewDialog

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Vérifier si le service a la méthode nécessaire
                if hasattr(service, 'print_completed_repair_receipt'):
                    # Générer le reçu de réparation
                    pdf_path = await service.print_completed_repair_receipt(repair_id)

                    # Afficher la prévisualisation
                    if pdf_path and os.path.exists(pdf_path):
                        dialog = PDFPreviewDialog(pdf_path, self, "Prévisualisation du reçu de réparation")
                        dialog.exec()
                    else:
                        QMessageBox.warning(self, "Avertissement", "Impossible de générer le reçu de réparation.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu de réparation n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation du reçu de réparation: {str(e)}")

    async def _preview_deposit_receipt_pdf_async(self, repair_id):
        """Version asynchrone de la prévisualisation du reçu de dépôt en PDF"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.ui.components.pdf_preview_dialog import PDFPreviewDialog

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Vérifier si le service a la méthode nécessaire
                if hasattr(service, 'print_repair_deposit_receipt'):
                    # Générer le reçu de dépôt
                    pdf_path = await service.print_repair_deposit_receipt(repair_id)

                    # Afficher la prévisualisation
                    if pdf_path and os.path.exists(pdf_path):
                        dialog = PDFPreviewDialog(pdf_path, self, "Prévisualisation du reçu de dépôt")
                        dialog.exec()
                    else:
                        QMessageBox.warning(self, "Avertissement", "Impossible de générer le reçu de dépôt.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu de dépôt n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation du reçu de dépôt: {str(e)}")

    async def _print_repair_receipt_pdf_async(self, repair_id):
        """Version asynchrone de l'impression du reçu de réparation en PDF"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Vérifier si le service a la méthode nécessaire
                if hasattr(service, 'print_completed_repair_receipt'):
                    # Générer le reçu de réparation
                    pdf_path = await service.print_completed_repair_receipt(repair_id)

                    # Ouvrir le PDF
                    if pdf_path:
                        DialogUtils.open_pdf(pdf_path, parent=self)
                    else:
                        QMessageBox.warning(self, "Avertissement", "Impossible de générer le reçu de réparation.")
                else:
                    QMessageBox.warning(self, "Avertissement", "La fonctionnalité d'impression de reçu de réparation n'est pas encore implémentée.")
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")

    async def _print_repair_receipt_thermal_async(self, repair_id):
        """Version asynchrone de l'impression du reçu de réparation sur une imprimante thermique"""
        try:
            from app.core.services.repair_service import RepairService
            from app.utils.database import SessionLocal
            from app.utils.escpos_printer import ESCPOSPrinter
            from app.ui.views.settings.dialogs.printer_config_dialog import PrinterConfigDialog

            # Vérifier si le module escpos est installé
            try:
                from app.utils.escpos_printer import ESCPOSPrinter
            except ImportError:
                import sys
                import subprocess
                QMessageBox.information(self, "Installation",
                    "Le module python-escpos est nécessaire pour l'impression thermique. Installation en cours...")
                try:
                    # Installer python-escpos
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-escpos>=3.0"])
                    # Réimporter après installation
                    from app.utils.escpos_printer import ESCPOSPrinter
                except Exception as install_error:
                    QMessageBox.critical(self, "Erreur d'installation",
                        f"Impossible d'installer python-escpos. Erreur : {str(install_error)}\n"
                        "Veuillez installer manuellement en exécutant :\n"
                        "pip install python-escpos>=3.0")
                    return

            db = SessionLocal()
            try:
                service = RepairService(db)

                # Récupérer les données de la réparation
                repair_data = await service.get_repair_with_details(repair_id)
                if not repair_data:
                    QMessageBox.warning(self, "Avertissement", f"Aucune donnée trouvée pour la réparation #{repair_id}")
                    return

                # Récupérer la configuration des imprimantes
                printer_config = PrinterConfigDialog.get_printer_config()
                default_printer = PrinterConfigDialog.get_default_printer()

                if default_printer:
                    # Utiliser l'imprimante par défaut
                    printer_name = default_printer["name"]
                    connection_type = default_printer.get("connection_type", "windows")
                    connection_params = default_printer.get("connection_params", {})

                    # Créer l'imprimante ESC/POS
                    printer = ESCPOSPrinter(
                        printer_name=printer_name,
                        connection_type=connection_type,
                        connection_params=connection_params
                    )

                    # Imprimer le reçu
                    success = printer.print_repair_receipt(repair_data)

                    if success:
                        QMessageBox.information(self, "Impression", f"Le reçu de réparation a été imprimé avec succès sur {printer_name}.")
                    else:
                        # Si l'impression échoue avec l'imprimante par défaut, demander à l'utilisateur de sélectionner une autre imprimante
                        QMessageBox.warning(
                            self,
                            "Avertissement",
                            f"Impossible d'imprimer le reçu de réparation sur l'imprimante par défaut ({printer_name}). Veuillez sélectionner une autre imprimante."
                        )
                        self._print_repair_receipt_thermal_with_selection(repair_data, printer_config)
                else:
                    # Aucune imprimante par défaut, demander à l'utilisateur de sélectionner une imprimante
                    self._print_repair_receipt_thermal_with_selection(repair_data, printer_config)
            finally:
                db.close()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")

    def _print_repair_receipt_thermal_with_selection(self, repair_data, printer_config):
        """Imprime un reçu de réparation sur une imprimante thermique sélectionnée par l'utilisateur"""
        try:
            from app.utils.escpos_printer import ESCPOSPrinter

            # Préparer la liste des imprimantes configurées
            configured_printers = list(printer_config.get("printers", {}).keys())

            if not configured_printers:
                # Si aucune imprimante n'est configurée, utiliser les imprimantes système
                printers = self._get_available_printers()
                if not printers:
                    QMessageBox.warning(self, "Avertissement", "Aucune imprimante n'a été trouvée.")
                    return

                printer_name, ok = QInputDialog.getItem(
                    self, "Sélectionner une imprimante",
                    "Choisissez une imprimante:", printers, 0, False
                )

                if not ok or not printer_name:
                    return

                # Créer l'imprimante ESC/POS
                printer = ESCPOSPrinter(printer_name=printer_name, connection_type="windows")
            else:
                # Utiliser les imprimantes configurées
                printer_name, ok = QInputDialog.getItem(
                    self, "Sélectionner une imprimante",
                    "Choisissez une imprimante:", configured_printers, 0, False
                )

                if not ok or not printer_name:
                    return

                # Récupérer la configuration de l'imprimante
                printer_config_item = printer_config.get("printers", {}).get(printer_name, {})
                connection_type = printer_config_item.get("connection_type", "windows")
                connection_params = printer_config_item.get("connection_params", {})

                # Créer l'imprimante ESC/POS
                printer = ESCPOSPrinter(
                    printer_name=printer_name,
                    connection_type=connection_type,
                    connection_params=connection_params
                )

            # Imprimer le reçu
            success = printer.print_repair_receipt(repair_data)

            if success:
                QMessageBox.information(self, "Impression", f"Le reçu de réparation a été imprimé avec succès sur {printer_name}.")
            else:
                QMessageBox.warning(self, "Avertissement", f"Impossible d'imprimer le reçu de réparation sur {printer_name}.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de réparation: {str(e)}")

    def _print_deposit_receipt_thermal_with_selection(self, repair_data, printer_config):
        """Imprime un reçu de dépôt sur une imprimante thermique sélectionnée par l'utilisateur"""
        try:
            from app.utils.escpos_printer import ESCPOSPrinter

            # Préparer la liste des imprimantes configurées
            configured_printers = list(printer_config.get("printers", {}).keys())

            if not configured_printers:
                # Si aucune imprimante n'est configurée, utiliser les imprimantes système
                printers = self._get_available_printers()
                if not printers:
                    QMessageBox.warning(self, "Avertissement", "Aucune imprimante n'a été trouvée.")
                    return

                printer_name, ok = QInputDialog.getItem(
                    self, "Sélectionner une imprimante",
                    "Choisissez une imprimante:", printers, 0, False
                )

                if not ok or not printer_name:
                    return

                # Créer l'imprimante ESC/POS
                printer = ESCPOSPrinter(printer_name=printer_name, connection_type="windows")
            else:
                # Utiliser les imprimantes configurées
                printer_name, ok = QInputDialog.getItem(
                    self, "Sélectionner une imprimante",
                    "Choisissez une imprimante:", configured_printers, 0, False
                )

                if not ok or not printer_name:
                    return

                # Récupérer la configuration de l'imprimante
                printer_config_item = printer_config.get("printers", {}).get(printer_name, {})
                connection_type = printer_config_item.get("connection_type", "windows")
                connection_params = printer_config_item.get("connection_params", {})

                # Créer l'imprimante ESC/POS
                printer = ESCPOSPrinter(
                    printer_name=printer_name,
                    connection_type=connection_type,
                    connection_params=connection_params
                )

            # Imprimer le reçu
            success = printer.print_repair_deposit_receipt(repair_data)

            if success:
                QMessageBox.information(self, "Impression", f"Le reçu de dépôt a été imprimé avec succès sur {printer_name}.")
            else:
                QMessageBox.warning(self, "Avertissement", f"Impossible d'imprimer le reçu de dépôt sur {printer_name}.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'impression du reçu de dépôt: {str(e)}")

    def _get_available_printers(self):
        """Récupère la liste des imprimantes disponibles"""
        try:
            # Vérifier si nous sommes sur Windows
            import platform
            if platform.system() != 'Windows':
                QMessageBox.warning(self, "Avertissement",
                    "L'impression directe n'est disponible que sous Windows.")
                return []

            # Essayer d'importer win32print avec une gestion d'erreur plus détaillée
            try:
                import win32print
                import win32api
            except ImportError as e:
                import sys
                import subprocess
                import os

                # Vérifier si nous sommes dans un environnement virtuel
                venv_path = os.environ.get('VIRTUAL_ENV')
                if venv_path:
                    pip_path = os.path.join(venv_path, 'Scripts', 'pip.exe')
                else:
                    pip_path = sys.executable.replace('python.exe', 'pip.exe')

                QMessageBox.information(self, "Installation",
                    "Le module win32print est nécessaire pour l'impression. Installation en cours...")

                try:
                    # Essayer d'installer avec pip
                    subprocess.check_call([pip_path, "install", "--upgrade", "pywin32>=305"])

                    # Vérifier si l'installation a réussi
                    try:
                        import win32print
                        import win32api
                    except ImportError:
                        raise Exception("L'installation de pywin32 a échoué")

                except Exception as install_error:
                    error_msg = str(install_error)
                    if "Permission denied" in error_msg:
                        QMessageBox.critical(self, "Erreur d'installation",
                            "Permission refusée lors de l'installation de pywin32.\n"
                            "Veuillez exécuter l'application en tant qu'administrateur.")
                    else:
                        QMessageBox.critical(self, "Erreur d'installation",
                            f"Impossible d'installer pywin32. Erreur : {error_msg}\n"
                            "Veuillez installer manuellement pywin32 en exécutant :\n"
                            "pip install pywin32>=305")
                    return []

            # Vérifier la version de Windows
            try:
                version = win32api.GetVersionEx()
                if version[0] < 10:  # Windows 10 ou supérieur requis
                    QMessageBox.warning(self, "Avertissement",
                        "Votre version de Windows pourrait ne pas être compatible avec certaines fonctionnalités d'impression.")
            except Exception as e:
                print(f"Erreur lors de la vérification de la version de Windows : {e}")

            # Récupérer la liste des imprimantes
            printers = []
            try:
                for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS):
                    printers.append(printer[2])
            except Exception as e:
                print(f"Erreur lors de l'énumération des imprimantes : {e}")
                QMessageBox.warning(self, "Avertissement",
                    "Impossible de récupérer la liste des imprimantes. Vérifiez vos permissions.")

            if not printers:
                QMessageBox.warning(self, "Avertissement",
                    "Aucune imprimante n'a été trouvée sur votre système.")

            return printers

        except Exception as e:
            error_msg = str(e)
            if "Access denied" in error_msg:
                QMessageBox.critical(self, "Erreur",
                    "Accès refusé lors de la récupération des imprimantes.\n"
                    "Veuillez exécuter l'application en tant qu'administrateur.")
            else:
                QMessageBox.critical(self, "Erreur",
                    f"Erreur lors de la récupération des imprimantes : {error_msg}\n"
                    "Veuillez vérifier que vous avez les droits d'administrateur nécessaires.")
            return []

    # Méthodes utilitaires
    def get_status_display(self, status):
        """Retourne l'affichage du statut"""
        status_display = {
            RepairStatus.PENDING: "En attente",
            RepairStatus.IN_PROGRESS: "En cours",
            RepairStatus.WAITING_PARTS: "En attente de pièces",
            RepairStatus.WAITING_FOR_PARTS: "En attente de pièces",
            RepairStatus.ON_HOLD: "En pause",
            RepairStatus.COMPLETED: "Terminé",
            RepairStatus.CANCELLED: "Annulé",
        }
        return status_display.get(status, str(status))

    def get_priority_display(self, priority):
        """Retourne l'affichage de la priorité"""
        priority_display = {
            RepairPriority.CRITICAL: "Critique",
            RepairPriority.HIGH: "Haute",
            RepairPriority.NORMAL: "Normale",
            RepairPriority.LOW: "Basse"
        }
        return priority_display.get(priority, str(priority))

    def get_payment_status_display(self, status):
        """Retourne l'affichage du statut de paiement (centralisé)"""
        from app.ui.utils.display_maps import payment_status_label
        return payment_status_label(status)





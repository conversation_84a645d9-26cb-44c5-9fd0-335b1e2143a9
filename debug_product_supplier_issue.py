#!/usr/bin/env python3
"""
Script de diagnostic pour le problème d'affichage des fournisseurs dans la sélection de produits
"""

import asyncio
import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.database import SessionLocal
from app.core.services.inventory_service import InventoryService
from app.core.services.supplier_service import SupplierService

async def debug_product_supplier_issue():
    """Diagnostique le problème d'affichage des fournisseurs"""
    
    print("=== DIAGNOSTIC DU PROBLÈME FOURNISSEURS ===\n")
    
    # Créer une session de base de données
    db = SessionLocal()
    
    try:
        # Initialiser les services
        inventory_service = InventoryService(db)
        supplier_service = SupplierService(db)
        
        print("1. Récupération des fournisseurs...")
        suppliers = await supplier_service.get_all()
        print(f"   Nombre de fournisseurs trouvés: {len(suppliers)}")
        
        if suppliers:
            print("   Liste des fournisseurs:")
            for supplier in suppliers:
                print(f"   - ID: {supplier.id}, Nom: {supplier.name}, Actif: {supplier.active}")
        else:
            print("   ⚠️  AUCUN FOURNISSEUR TROUVÉ!")
            return
        
        print("\n2. Récupération des produits...")
        products = await inventory_service.get_all()
        print(f"   Nombre de produits trouvés: {len(products)}")
        
        if not products:
            print("   ⚠️  AUCUN PRODUIT TROUVÉ!")
            return
        
        print("\n3. Analyse des associations produit-fournisseur:")
        supplier_counts = {}
        products_without_supplier = 0
        
        for product in products:
            if product.supplier_id:
                if product.supplier_id in supplier_counts:
                    supplier_counts[product.supplier_id] += 1
                else:
                    supplier_counts[product.supplier_id] = 1
            else:
                products_without_supplier += 1
        
        print(f"   Produits sans fournisseur: {products_without_supplier}")
        print("   Répartition par fournisseur:")
        
        for supplier_id, count in supplier_counts.items():
            # Trouver le nom du fournisseur
            supplier_name = "Inconnu"
            for supplier in suppliers:
                if supplier.id == supplier_id:
                    supplier_name = supplier.name
                    break
            print(f"   - Fournisseur {supplier_id} ({supplier_name}): {count} produits")
        
        print("\n4. Test de la logique d'affichage:")
        print("   Simulation de l'affichage des 10 premiers produits:")
        
        for i, product in enumerate(products[:10]):
            # Simuler la logique d'affichage du fournisseur
            supplier_name = "Non spécifié"
            if product.supplier_id:
                for supplier in suppliers:
                    if supplier.id == product.supplier_id:
                        supplier_name = supplier.name
                        break
            
            print(f"   {i+1}. {product.name} (SKU: {product.sku}) -> Fournisseur: {supplier_name}")
        
        print("\n5. Vérification des données de test:")
        
        # Vérifier s'il y a des données de test avec différents fournisseurs
        unique_supplier_ids = set()
        for product in products:
            if product.supplier_id:
                unique_supplier_ids.add(product.supplier_id)
        
        print(f"   Nombre de fournisseurs uniques utilisés: {len(unique_supplier_ids)}")
        print(f"   IDs des fournisseurs utilisés: {list(unique_supplier_ids)}")
        
        if len(unique_supplier_ids) <= 1:
            print("   ⚠️  PROBLÈME DÉTECTÉ: Tous les produits utilisent le même fournisseur ou aucun!")
            print("   Cela explique pourquoi tous les produits affichent le même fournisseur.")
            
            # Proposer une solution
            print("\n6. SOLUTION PROPOSÉE:")
            print("   Il faut créer plusieurs fournisseurs et associer les produits à différents fournisseurs.")
            print("   Voulez-vous que je crée des fournisseurs de test et répartisse les produits?")
        else:
            print("   ✅ Les produits sont associés à différents fournisseurs.")
            print("   Le problème pourrait être dans l'interface utilisateur.")
    
    except Exception as e:
        print(f"❌ Erreur lors du diagnostic: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

async def create_test_suppliers_and_redistribute():
    """Crée des fournisseurs de test et redistribue les produits"""
    
    print("\n=== CRÉATION DE FOURNISSEURS DE TEST ===\n")
    
    db = SessionLocal()
    
    try:
        supplier_service = SupplierService(db)
        inventory_service = InventoryService(db)
        
        # Créer des fournisseurs de test
        test_suppliers = [
            {
                "name": "TechnoPlus SARL",
                "contact_person": "Ahmed Benali",
                "email": "<EMAIL>",
                "phone": "0555123456",
                "address": "Zone Industrielle, Alger",
                "commune": "Rouiba",
                "city": "Alger",
                "rating": "EXCELLENT"
            },
            {
                "name": "ElectroMag Distribution",
                "contact_person": "Fatima Khelifi",
                "email": "<EMAIL>", 
                "phone": "0661789012",
                "address": "Rue des Frères Bouadou, Oran",
                "commune": "Es Senia",
                "city": "Oran",
                "rating": "GOOD"
            },
            {
                "name": "Informatique Solutions",
                "contact_person": "Karim Meziane",
                "email": "<EMAIL>",
                "phone": "0770345678",
                "address": "Boulevard Mohamed V, Constantine",
                "commune": "Constantine",
                "city": "Constantine", 
                "rating": "GOOD"
            }
        ]
        
        created_suppliers = []
        
        for supplier_data in test_suppliers:
            # Vérifier si le fournisseur existe déjà
            existing = db.query(supplier_service.model).filter(
                supplier_service.model.name == supplier_data["name"]
            ).first()
            
            if not existing:
                from app.core.models.supplier import Supplier, SupplierRating
                
                supplier = Supplier(
                    name=supplier_data["name"],
                    contact_person=supplier_data["contact_person"],
                    email=supplier_data["email"],
                    phone=supplier_data["phone"],
                    address=supplier_data["address"],
                    commune=supplier_data["commune"],
                    city=supplier_data["city"],
                    rating=SupplierRating[supplier_data["rating"]],
                    active=True
                )
                
                db.add(supplier)
                db.commit()
                db.refresh(supplier)
                created_suppliers.append(supplier)
                print(f"✅ Fournisseur créé: {supplier.name} (ID: {supplier.id})")
            else:
                created_suppliers.append(existing)
                print(f"ℹ️  Fournisseur existant: {existing.name} (ID: {existing.id})")
        
        # Redistribuer les produits
        products = await inventory_service.get_all()
        
        if products and created_suppliers:
            print(f"\n📦 Redistribution de {len(products)} produits...")
            
            for i, product in enumerate(products):
                # Assigner un fournisseur de manière cyclique
                supplier = created_suppliers[i % len(created_suppliers)]
                product.supplier_id = supplier.id
                
                print(f"   {product.name} -> {supplier.name}")
            
            db.commit()
            print(f"\n✅ {len(products)} produits redistribués avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
    
    finally:
        db.close()

if __name__ == "__main__":
    print("Script de diagnostic des fournisseurs\n")
    
    # Exécuter le diagnostic
    asyncio.run(debug_product_supplier_issue())
    
    # Demander si on veut créer des fournisseurs de test
    response = input("\nVoulez-vous créer des fournisseurs de test et redistribuer les produits? (o/n): ")
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        asyncio.run(create_test_suppliers_and_redistribute())
        print("\n🔄 Relancement du diagnostic après modification...")
        asyncio.run(debug_product_supplier_issue())

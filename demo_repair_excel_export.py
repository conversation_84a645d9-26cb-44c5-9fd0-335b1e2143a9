#!/usr/bin/env python3
"""
Démonstration de la fonctionnalité d'export Excel des réparations
"""

import sys
import os
import tempfile

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import Qt

def create_demo_excel_export():
    """Crée un fichier Excel de démonstration avec les données des réparations"""
    
    print("=== DÉMONSTRATION D'EXPORT EXCEL DES RÉPARATIONS ===\n")
    
    try:
        import pandas as pd
        from app.utils.export_utils import export_to_excel
        from datetime import datetime
        
        # Créer des données de démonstration réalistes
        repairs_data = pd.DataFrame({
            "Numéro": [
                "REP-20250921-001",
                "REP-20250921-002", 
                "REP-20250921-003",
                "REP-20250921-004",
                "REP-20250921-005"
            ],
            "Client": [
                "Ahmed Benali",
                "Fatima Khelifi", 
                "Karim Meziane",
                "Smail Benai",
                "Nadia Boudiaf"
            ],
            "Téléphone": [
                "0555123456",
                "0661789012",
                "0770345678", 
                "0698765432",
                "0777654321"
            ],
            "Marque": [
                "Samsung",
                "Apple",
                "Huawei",
                "Samsung", 
                "Xiaomi"
            ],
            "Modèle": [
                "Galaxy S21",
                "iPhone 12",
                "P30 Pro",
                "A06",
                "Redmi Note 10"
            ],
            "N° Série": [
                "SN123456789",
                "SN987654321",
                "SN456789123",
                "SN789123456",
                "SN321654987"
            ],
            "Statut": [
                "En cours",
                "Terminé",
                "En attente",
                "En cours",
                "Diagnostiqué"
            ],
            "Priorité": [
                "Normale",
                "Haute",
                "Normale",
                "Urgente",
                "Normale"
            ],
            "Technicien": [
                "Mohamed Saidi",
                "Ali Benaissa",
                "Mohamed Saidi",
                "Youcef Hamdi",
                "Ali Benaissa"
            ],
            "Prix estimé": [
                "2500.00 DA",
                "3200.00 DA",
                "1800.00 DA",
                "2100.00 DA",
                "2800.00 DA"
            ],
            "Date prévue": [
                "25/09/2025",
                "22/09/2025",
                "28/09/2025",
                "24/09/2025",
                "26/09/2025"
            ],
            "Date de création": [
                "21/09/2025",
                "20/09/2025",
                "21/09/2025",
                "21/09/2025",
                "21/09/2025"
            ]
        })
        
        # Créer les données de résumé
        summary_data = pd.DataFrame({
            "Métrique": [
                "Nombre total de réparations",
                "Date d'export",
                "Réparations En cours",
                "Réparations Terminé",
                "Réparations En attente",
                "Réparations Diagnostiqué",
                "Techniciens actifs",
                "Marques traitées"
            ],
            "Valeur": [
                5,
                datetime.now().strftime("%d/%m/%Y %H:%M"),
                2,
                1,
                1,
                1,
                3,
                4
            ]
        })
        
        # Créer les données par statut
        status_data = pd.DataFrame({
            "Statut": ["En cours", "Terminé", "En attente", "Diagnostiqué"],
            "Nombre": [2, 1, 1, 1],
            "Pourcentage": ["40.0%", "20.0%", "20.0%", "20.0%"]
        })
        
        # Créer les données par technicien
        technician_data = pd.DataFrame({
            "Technicien": ["Mohamed Saidi", "Ali Benaissa", "Youcef Hamdi"],
            "Nombre de réparations": [2, 2, 1],
            "Pourcentage": ["40.0%", "40.0%", "20.0%"]
        })
        
        # Créer les données par marque
        brand_data = pd.DataFrame({
            "Marque": ["Samsung", "Apple", "Huawei", "Xiaomi"],
            "Nombre": [2, 1, 1, 1],
            "Pourcentage": ["40.0%", "20.0%", "20.0%", "20.0%"]
        })
        
        # Préparer toutes les données pour l'export
        export_data = {
            "Réparations": repairs_data,
            "Résumé": summary_data,
            "Par Statut": status_data,
            "Par Technicien": technician_data,
            "Par Marque": brand_data
        }
        
        # Créer le fichier Excel
        output_path = os.path.expanduser("~/Documents/Demo_Réparations_Export.xlsx")
        
        if export_to_excel(export_data, output_path):
            print("✅ Fichier Excel de démonstration créé avec succès!")
            print(f"📁 Emplacement: {output_path}")
            print(f"📊 Onglets créés: {list(export_data.keys())}")
            
            # Vérifier la taille du fichier
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📏 Taille du fichier: {file_size} bytes")
                
                # Afficher un aperçu des données
                print("\n📋 APERÇU DES DONNÉES:")
                print("   Réparations:")
                for _, row in repairs_data.head(3).iterrows():
                    print(f"   - {row['Numéro']}: {row['Client']} ({row['Marque']} {row['Modèle']}) - {row['Statut']}")
                
                print(f"\n   Statistiques:")
                print(f"   - Total: {len(repairs_data)} réparations")
                print(f"   - En cours: {len(repairs_data[repairs_data['Statut'] == 'En cours'])} réparations")
                print(f"   - Terminées: {len(repairs_data[repairs_data['Statut'] == 'Terminé'])} réparations")
                
                return output_path
            else:
                print("❌ Le fichier n'a pas été créé")
                return None
        else:
            print("❌ Échec de la création du fichier Excel")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lors de la création du fichier de démonstration: {e}")
        import traceback
        traceback.print_exc()
        return None

def show_demo_instructions():
    """Affiche les instructions pour utiliser la fonctionnalité d'export"""
    
    print("\n" + "="*60)
    print("📖 GUIDE D'UTILISATION DE L'EXPORT EXCEL DES RÉPARATIONS")
    print("="*60)
    
    print("\n🚀 COMMENT UTILISER LA FONCTIONNALITÉ:")
    print("   1. Lancez l'application principale (main.py)")
    print("   2. Connectez-vous avec vos identifiants")
    print("   3. Naviguez vers le module 'Réparations'")
    print("   4. Cliquez sur le bouton 'Exporter' dans la barre d'outils")
    print("      OU cliquez sur le bouton flottant '⇩' en bas à droite")
    print("   5. Choisissez le format d'export:")
    print("      • Excel (.xlsx) - Recommandé pour l'analyse")
    print("      • CSV (.csv) - Compatible avec tous les tableurs")
    print("   6. Sélectionnez l'emplacement de sauvegarde")
    print("   7. Le fichier sera créé automatiquement")
    
    print("\n📊 CONTENU DU FICHIER EXCEL:")
    print("   • Onglet 'Réparations': Toutes les données des réparations")
    print("   • Onglet 'Résumé': Statistiques générales et date d'export")
    print("   • Onglet 'Par Statut': Répartition des réparations par statut")
    print("   • Onglet 'Par Technicien': Charge de travail par technicien")
    print("   • Onglet 'Par Marque': Répartition par marque d'équipement")
    
    print("\n💡 AVANTAGES DE L'EXPORT EXCEL:")
    print("   ✅ Analyse avancée avec formules et graphiques")
    print("   ✅ Filtrage et tri des données")
    print("   ✅ Partage facile avec d'autres services")
    print("   ✅ Archivage et sauvegarde des données")
    print("   ✅ Rapports personnalisés")
    
    print("\n🔧 DÉPANNAGE:")
    print("   • Si l'export échoue, vérifiez l'espace disque disponible")
    print("   • Assurez-vous que le fichier de destination n'est pas ouvert")
    print("   • Vérifiez les permissions d'écriture du dossier")
    print("   • En cas d'erreur, essayez l'export CSV en alternative")

def main():
    """Fonction principale de démonstration"""
    
    print("🎯 DÉMONSTRATION DE L'EXPORT EXCEL DES RÉPARATIONS\n")
    
    # Créer le fichier de démonstration
    demo_file = create_demo_excel_export()
    
    # Afficher les instructions
    show_demo_instructions()
    
    if demo_file:
        print(f"\n🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS!")
        print(f"📁 Fichier de démonstration créé: {demo_file}")
        print("\n💻 Vous pouvez maintenant:")
        print("   1. Ouvrir le fichier Excel pour voir le résultat")
        print("   2. Tester la fonctionnalité dans l'application")
        print("   3. Exporter vos vraies données de réparations")
    else:
        print("\n⚠️  La démonstration a échoué")
        print("🔧 Vérifiez les erreurs ci-dessus et réessayez")

if __name__ == "__main__":
    main()
